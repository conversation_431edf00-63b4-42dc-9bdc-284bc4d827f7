// 存储已打开的标签页ID
let openedTabId = null;  // 原有的打单标签页ID
let openedBatchPrintTabId = null;  // 批量打印标签页ID
let openedAssistantTabId = null;   // 店铺助手标签页ID

// 在文件开头添加
let isExtensionEnabled = true;

// 初始化时获取扩展状态
chrome.storage.local.get('isExtensionEnabled', function(data) {
    // 如果是首次加载（没有存储的状态），则设置为启用状态
    if (data.isExtensionEnabled === undefined) {
        chrome.storage.local.set({ isExtensionEnabled: true });
        isExtensionEnabled = true;
    } else {
        isExtensionEnabled = data.isExtensionEnabled;
    }
});

// 监听存储变化
chrome.storage.onChanged.addListener(function(changes, namespace) {
    if (changes.isExtensionEnabled) {
        isExtensionEnabled = changes.isExtensionEnabled.newValue;
    }
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'toggleExtension') {
        isExtensionEnabled = request.enabled;
        return;
    }
    
    // 如果扩展被禁用，不处理其他消息
    if (!isExtensionEnabled) {
        return;
    }

    if (request.action === 'openStockTabs') {
        console.log('处理入库存标签页请求');
        
        // 先检查店铺助手标签页
        if (openedAssistantTabId) {
            chrome.tabs.get(openedAssistantTabId, (tab) => {
                if (chrome.runtime.lastError) {
                    // 如果标签页不存在，创建新标签页
                    createAssistantTab();
                } else {
                    // 如果标签页存在，只更新它的状态
                    chrome.tabs.update(openedAssistantTabId, { active: false });
                }
            });
        } else {
            createAssistantTab();
        }

        // 延迟500ms后处理批量打印标签页
        setTimeout(() => {
            if (openedBatchPrintTabId) {
                chrome.tabs.get(openedBatchPrintTabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        // 如果标签页不存在，创建新标签页
                        createBatchPrintTab(request.orderId);
                    } else {
                        // 如果标签页存在，更新它并发送订单号
                        chrome.tabs.update(openedBatchPrintTabId, { active: true }, (tab) => {
                            setTimeout(() => {
                                injectScriptToFrames(tab.id, request.orderId);
                            }, 500);
                        });
                    }
                });
            } else {
                createBatchPrintTab(request.orderId);
            }
        }, 500);
    } else if (request.action === 'openNewTab') {
        console.log('处理打开标签页请求:', request.url);
        
        // 检查是否已有打开的标签页
        if (openedTabId) {
            // 检查标签页是否仍然存在
            chrome.tabs.get(openedTabId, (tab) => {
                if (chrome.runtime.lastError) {
                    // 如果标签页不存在，创建新标签页
                    createNewTab(request.url, request.orderId);
                } else {
                    // 如果标签页存在，更新它并发送新的订单号
                    chrome.tabs.update(openedTabId, { active: true }, (tab) => {
                        setTimeout(() => {
                            injectScriptToFrames(tab.id, request.orderId);
                        }, 500);
                    });
                }
            });
        } else {
            // 如果没有已打开的标签页，创建新标签页
            createNewTab(request.url, request.orderId);
        }
    }
    else if (request.action === 'copyToClipboard') {
        // 在活动标签页中执行复制操作
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    func: (text) => {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        const success = document.execCommand('copy');
                        document.body.removeChild(textArea);
                        return success;
                    },
                    args: [request.text]
                }).then(results => {
                    const success = results && results[0] && results[0].result;
                    sendResponse({ success: success });
                }).catch(error => {
                    console.error('复制失败:', error);
                    sendResponse({ success: false, error: error.message });
                });
            } else {
                sendResponse({ success: false, error: 'No active tab found' });
            }
        });
        return true;
    }
    else if (request.type === 'nativeMessage') {
        // 连接到本地程序
        const port = chrome.runtime.connectNative('com.my_automation');
        
        port.onMessage.addListener((response) => {
            sendResponse(response);
        });
        
        port.onDisconnect.addListener(() => {
            if (chrome.runtime.lastError) {
                console.error('本地程序连接断开:', chrome.runtime.lastError);
            }
        });
        
        // 发送消息给本地程序
        port.postMessage(request);
        return true; // 保持消息通道开放
    }
    else if (request.action === 'focusScanPage') {
        // 通过标题查找扫描页面
        chrome.tabs.query({}, function(tabs) {
            const scanTab = tabs.find(tab => 
                tab.title && tab.title.includes('悦好') && 
                tab.url && tab.url.includes('refund.yuehao.com/record/scan')
            );
            
            if (scanTab) {
                // 激活找到的标签页
                chrome.tabs.update(scanTab.id, {active: true}, function(tab) {
                    // 注入脚本聚焦输入框
                    chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        function: () => {
                            const inputElement = document.querySelector('input#code.ant-input');
                            if (inputElement) {
                                inputElement.focus();
                            }
                        }
                    });
                });
            } else {
                console.error('未找到扫描页面标签');
            }
        });
    } else if (request.action === 'openAssistantTab') {
        console.log('处理打开店铺助手请求，商品简称:', request.shortCode);
        
        // 检查是否有已打开的店铺助手标签页
        if (openedAssistantTabId) {
            chrome.tabs.get(openedAssistantTabId, (tab) => {
                if (chrome.runtime.lastError) {
                    // 如果标签页不存在，创建新标签页
                    createAssistantTab(request.shortCode, request.skuInfo);
                } else {
                    // 如果标签页存在，先激活窗口和标签页
                    chrome.windows.update(tab.windowId, { focused: true }, () => {
                        chrome.tabs.update(tab.id, { active: true }, (updatedTab) => {
                            console.log('店铺助手标签页已激活，等待页面稳定...');
                            // 等待页面完全激活后再发送消息
                            setTimeout(() => {
                                console.log('开始发送搜索消息...');
                                chrome.tabs.sendMessage(updatedTab.id, {
                                    action: 'searchShortCode',
                                    shortCode: request.shortCode,
                                    skuInfo: request.skuInfo
                                }, response => {
                                    if (chrome.runtime.lastError) {
                                        console.error('发送搜索消息失败:', chrome.runtime.lastError);
                                    } else {
                                        console.log('搜索消息发送成功');
                                    }
                                });
                            }, 1000);
                        });
                    });
                }
            });
        } else {
            // 如果没有记录的标签页ID，创建新的
            createAssistantTab(request.shortCode, request.skuInfo);
        }
        return true;
    }
    return true;
});

// 创建新标签页的函数
function createNewTab(url, orderId) {
    chrome.tabs.create({ 
        url: url,
        active: true
    }, (tab) => {
        openedTabId = tab.id;
        console.log('新标签页已创建:', tab.id);
        
        chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
            if (tabId === tab.id && info.status === 'complete') {
                console.log('标签页加载完成');
                chrome.tabs.onUpdated.removeListener(listener);
                
                setTimeout(() => {
                    injectScriptToFrames(tab.id, orderId);
                }, 2000);
            }
        });
    });
}

// 注入脚本到框架的函数
function injectScriptToFrames(tabId, orderId) {
    // 添加一个标记来防止重复注入
    chrome.scripting.executeScript({
        target: { tabId: tabId, allFrames: true },
        func: (orderId) => {
            // 检查是否已经注入
            if (window._orderHelperInjected) {
                // 如果已注入，只发送消息
                window.postMessage({
                    type: 'FILL_ORDER_ID',
                    orderId: orderId
                }, '*');
                return;
            }
            
            // 标记为已注入
            window._orderHelperInjected = true;
            
            console.log('脚本已注入到框架, 订单号:', orderId);
            window.postMessage({
                type: 'FILL_ORDER_ID',
                orderId: orderId
            }, '*');
        },
        args: [orderId]
    }).catch(err => console.error('脚本注入错误:', err));
}

// 添加创建批量打印标签页的函数
function createBatchPrintTab(orderId) {
    chrome.tabs.create({ 
        url: 'https://p51.kuaidizs.cn/newIndex/index.xhtml?appkey=12158997&taobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7&taobaoId=506699383&version=premium&isNewUser=0&isFromXcx=0&subTaobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7%3A%E5%B0%8F%E4%B8%9C&subTaobaoId=2674282203&v=1739010590282#/printBatch/',
        active: true
    }, (tab) => {
        openedBatchPrintTabId = tab.id;
        console.log('批量打印标签页已创建:', tab.id);
        
        // 等待页面加载完成后注入订单号
        chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
            if (tabId === tab.id && info.status === 'complete') {
                console.log('批量打印标签页加载完成');
                chrome.tabs.onUpdated.removeListener(listener);
                
                setTimeout(() => {
                    injectScriptToFrames(tab.id, orderId);
                }, 2000);
            }
        });
    });
}

// 添加创建店铺助手标签页的函数
function createAssistantTab(shortCode, skuInfo) {
    chrome.tabs.create({ 
        url: 'https://p51.kuaidizs.cn/newIndex/index.xhtml?appkey=12158997&taobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7&taobaoId=506699383&version=premium&isNewUser=0&isFromXcx=0&subTaobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7%3A%E5%B0%8F%E4%B8%9C&subTaobaoId=2674282203&v=1739010590282#/assistant/?menu=stock',
        active: false
    }, (tab) => {
        openedAssistantTabId = tab.id;
        console.log('店铺助手标签页已创建:', tab.id);

        // 如果提供了商品简称，等待页面加载完成后发送搜索消息
        if (shortCode) {
            chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
                if (tabId === tab.id && info.status === 'complete') {
                    console.log('店铺助手页面加载完成，准备发送搜索消息');
                    chrome.tabs.onUpdated.removeListener(listener);
                    
                    // 等待页面完全加载
                    setTimeout(() => {
                        chrome.windows.update(tab.windowId, { focused: true }, () => {
                            chrome.tabs.update(tab.id, { active: true }, (updatedTab) => {
                                console.log('店铺助手标签页已激活，等待页面稳定...');
                                // 等待页面完全激活后再发送消息
                                setTimeout(() => {
                                    console.log('开始发送搜索消息...');
                                    chrome.tabs.sendMessage(updatedTab.id, {
                                        action: 'searchShortCode',
                                        shortCode: shortCode,
                                        skuInfo: skuInfo
                                    }, response => {
                                        if (chrome.runtime.lastError) {
                                            console.error('发送搜索消息失败:', chrome.runtime.lastError);
                                        } else {
                                            console.log('搜索消息发送成功');
                                        }
                                    });
                                }, 1000);
                            });
                        });
                    }, 1000);
                }
            });
        }
    });
}

// 监听标签页关闭事件
chrome.tabs.onRemoved.addListener((tabId) => {
    if (tabId === openedTabId) {
        openedTabId = null;
    } else if (tabId === openedBatchPrintTabId) {
        openedBatchPrintTabId = null;
    } else if (tabId === openedAssistantTabId) {
        openedAssistantTabId = null;
    }
});