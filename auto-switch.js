// 创建一个函数来检查和改变下拉框值
function changeDropdownValue() {
    const dropdownValue = document.querySelector('.drop-down-value[name="searchType"]');
    if (dropdownValue) {
        dropdownValue.textContent = '商品简称';
        dropdownValue.setAttribute('data-value', '4');
        
        const dropdownItems = document.querySelectorAll('.drop-down-select li');
        dropdownItems.forEach(item => {
            if (item.getAttribute('value') === '4') {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
        console.log('下拉框切换成功');
        return true;
    }
    return false;
}

// 创建一个观察器来监视DOM变化
function setupObserver() {
    let attempts = 0;
    const maxAttempts = 50;
    const interval = 200;

    function checkElement() {
        if (attempts >= maxAttempts) {
            console.log('达到最大尝试次数，停止检查');
            return;
        }

        console.log('尝试切换下拉框，第', attempts + 1, '次');
        if (!changeDropdownValue()) {
            attempts++;
            setTimeout(checkElement, interval);
        }
    }

    // 监听DOM变化
    const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            if (mutation.addedNodes.length) {
                checkElement();
            }
        }
    });

    observer.observe(document.documentElement, {
        childList: true,
        subtree: true
    });

    // 立即开始第一次检查
    checkElement();
}

// 检查当前页面URL并决定是否执行
function init() {
    const currentUrl = window.location.href;
    console.log('当前页面URL:', currentUrl);
    
    if (
        currentUrl.includes('/stock/') ||
        currentUrl.includes('menu=stock') ||
        (window.location.hash && (
            window.location.hash.includes('/stock/') ||
            window.location.hash.includes('menu=stock')
        ))
    ) {
        console.log('检测到库存页面，开始设置观察器');
        setupObserver();
    }
}

// 在页面加载完成后执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

// 监听URL变化
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        init();
    }
}).observe(document, {subtree: true, childList: true}); 