import sys
import json
import struct
import win32gui
import win32con
import win32api
import time
import re
from win32con import VK_RIGHT

def get_message():
    raw_length = sys.stdin.buffer.read(4)
    if not raw_length:
        return None
    message_length = struct.unpack('=I', raw_length)[0]
    message = sys.stdin.buffer.read(message_length)
    return json.loads(message)

def send_message(message):
    encoded_message = json.dumps(message).encode('utf-8')
    encoded_length = struct.pack('=I', len(encoded_message))
    sys.stdout.buffer.write(encoded_length)
    sys.stdout.buffer.write(encoded_message)
    sys.stdout.buffer.flush()

def press_right_arrow():
    # 模拟按下右方向键
    win32api.keybd_event(VK_RIGHT, 0, 0, 0)  # 按下
    time.sleep(0.1)  # 短暂延迟
    win32api.keybd_event(VK_RIGHT, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
    return {"status": "success", "message": "Right arrow key pressed"}

def find_window_and_click_button():
    # 使用正则表达式匹配窗口标题
    window_pattern = re.compile(r'店群售后神器v1\.5\.[0-9a-f]+')
    
    def enum_window_callback(hwnd, windows):
        window_text = win32gui.GetWindowText(hwnd)
        if window_pattern.match(window_text):
            windows.append(hwnd)
        return True
    
    windows = []
    win32gui.EnumWindows(enum_window_callback, windows)
    
    if not windows:
        return {"status": "error", "message": "找不到店群售后神器窗口"}
    
    hwnd = windows[0]  # 使用找到的第一个匹配窗口
    
    # 激活窗口
    if win32gui.IsIconic(hwnd):  # 如果窗口是最小化的
        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
    win32gui.SetForegroundWindow(hwnd)
    time.sleep(0.5)  # 等待窗口激活
    
    # 查找并点击"粘贴并搜索"按钮
    def find_paste_button(hwnd, param):
        if win32gui.GetWindowText(hwnd) == "粘贴并搜索":
            param.append(hwnd)
        return True
    
    buttons = []
    win32gui.EnumChildWindows(hwnd, find_paste_button, buttons)
    
    if not buttons:
        return {"status": "error", "message": "找不到粘贴并搜索按钮"}
    
    # 点击按钮
    button_hwnd = buttons[0]
    win32gui.SendMessage(button_hwnd, win32con.BM_CLICK, 0, 0)
    
    # 等待一小段时间
    time.sleep(0.5)
    
    # 点击左上角悦好标签（使用准确坐标）
    win32api.SetCursorPos((82, 35))
    time.sleep(0.1)
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
    
    # 等待标签切换
    time.sleep(0.8)  # 增加等待时间确保页面加载
    
    # 点击搜索框（使用准确坐标）
    win32api.SetCursorPos((285, 335))
    time.sleep(0.1)
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
    
    return {"status": "success"}

def main():
    while True:
        try:
            message = get_message()
            if message is None:
                break
                
            if message.get('action') == 'switch_and_paste':
                result = find_window_and_click_button()
                send_message(result)
            elif message.get('action') == 'press_right_arrow':
                result = press_right_arrow()
                send_message(result)
        except Exception as e:
            send_message({"status": "error", "message": str(e)})

if __name__ == '__main__':
    main()
