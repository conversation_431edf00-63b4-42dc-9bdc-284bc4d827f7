// 存储已打开的标签页ID
let openedTabId = null;

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'openNewTab') {
        console.log('处理打开标签页请求:', request.url);
        
        // 检查是否已有打开的标签页
        if (openedTabId) {
            // 检查标签页是否仍然存在
            chrome.tabs.get(openedTabId, (tab) => {
                if (chrome.runtime.lastError) {
                    // 如果标签页不存在，创建新标签页
                    createNewTab(request.url, request.orderId);
                } else {
                    // 如果标签页存在，更新它并发送新的订单号
                    chrome.tabs.update(openedTabId, { active: true }, (tab) => {
                        setTimeout(() => {
                            injectScriptToFrames(tab.id, request.orderId);
                        }, 500);
                    });
                }
            });
        } else {
            // 如果没有已打开的标签页，创建新标签页
            createNewTab(request.url, request.orderId);
        }
    }
    else if (request.action === 'copyToClipboard') {
        // 在活动标签页中执行复制操作
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    func: (text) => {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        const success = document.execCommand('copy');
                        document.body.removeChild(textArea);
                        return success;
                    },
                    args: [request.text]
                }).then(results => {
                    const success = results && results[0] && results[0].result;
                    sendResponse({ success: success });
                }).catch(error => {
                    console.error('复制失败:', error);
                    sendResponse({ success: false, error: error.message });
                });
            } else {
                sendResponse({ success: false, error: 'No active tab found' });
            }
        });
        return true;
    }
    else if (request.type === 'nativeMessage') {
        // 连接到本地程序
        const port = chrome.runtime.connectNative('com.my_automation');
        
        port.onMessage.addListener((response) => {
            sendResponse(response);
        });
        
        port.onDisconnect.addListener(() => {
            if (chrome.runtime.lastError) {
                console.error('本地程序连接断开:', chrome.runtime.lastError);
            }
        });
        
        // 发送消息给本地程序
        port.postMessage(request);
        return true; // 保持消息通道开放
    }
    return true;
});

// 创建新标签页的函数
function createNewTab(url, orderId) {
    chrome.tabs.create({ 
        url: url,
        active: true
    }, (tab) => {
        openedTabId = tab.id;
        console.log('新标签页已创建:', tab.id);
        
        chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
            if (tabId === tab.id && info.status === 'complete') {
                console.log('标签页加载完成');
                chrome.tabs.onUpdated.removeListener(listener);
                
                setTimeout(() => {
                    injectScriptToFrames(tab.id, orderId);
                }, 2000);
            }
        });
    });
}

// 注入脚本到框架的函数
function injectScriptToFrames(tabId, orderId) {
    chrome.scripting.executeScript({
        target: { tabId: tabId, allFrames: true },
        func: (orderId) => {
            console.log('脚本已注入到框架, 订单号:', orderId);
            window.postMessage({
                type: 'FILL_ORDER_ID',
                orderId: orderId
            }, '*');
        },
        args: [orderId]
    }).catch(err => console.error('脚本注入错误:', err));
}

// 监听标签页关闭事件
chrome.tabs.onRemoved.addListener((tabId) => {
    if (tabId === openedTabId) {
        openedTabId = null;
    }
}); 
