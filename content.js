// 在文件开头添加状态检查
let isExtensionEnabled = true;

// 添加已处理订单集合
const processedOrders = new Set();

// 添加一个标志来防止重复操作
let isProcessing = false;

// 添加全局变量来存储产品信息
let currentProductInfo = null;

// 初始化时获取扩展状态
chrome.storage.local.get('isExtensionEnabled', function(data) {
    // 如果是首次加载（没有存储的状态），则设置为启用状态
    if (data.isExtensionEnabled === undefined) {
        chrome.storage.local.set({ isExtensionEnabled: true });
        isExtensionEnabled = true;
        addButtons(); // 立即添加按钮
    } else {
        isExtensionEnabled = data.isExtensionEnabled;
        if (isExtensionEnabled) {
            addButtons(); // 如果是启用状态，添加按钮
        } else {
            removeAllButtons(); // 如果是禁用状态，移除按钮
        }
    }
});

// 监听扩展状态变化
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'extensionStateChanged') {
        isExtensionEnabled = request.enabled;
        if (isExtensionEnabled) {
            addButtons();
        } else {
            removeAllButtons();
        }
    }
});

function addButtons() {
    // 如果扩展被禁用，直接返回
    if (!isExtensionEnabled) {
        return;
    }
    
    // 查找订单链接元素
    const orderLinks = document.querySelectorAll('a.tradeUrl___16K0h');
    
    orderLinks.forEach(link => {
        // 检查是否已经添加过按钮
        if (link.nextElementSibling?.classList.contains('custom-buttons-container')) {
            return;
        }
        
        // 获取订单号
        const orderId = link.textContent;
        
        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'custom-buttons-container';
        
        // 创建入库存按钮
        const stockButton = document.createElement('button');
        stockButton.textContent = '入库存';
        stockButton.className = 'custom-button';
        stockButton.onclick = () => {
            handleButtonClick(orderId, 'stock');
        };
        
        // 创建打单按钮
        const printButton = document.createElement('button');
        printButton.textContent = '打单';
        printButton.className = 'custom-button';
        printButton.onclick = () => {
            handleButtonClick(orderId, 'print');
        };
        
        // 添加按钮到容器
        buttonContainer.appendChild(stockButton);
        buttonContainer.appendChild(printButton);
        
        // 将按钮容器插入到链接后面
        link.parentNode.insertBefore(buttonContainer, link.nextSibling);
    });
}

// 添加域名检测函数
async function detectValidDomain() {
    const baseDomains = [
        'handorderg.kuaidizs.cn',
        'p51.kuaidizs.cn',
        'handorder2.kuaidizs.cn'
    ];
    
    // 检测哪个域名可用
    for (const domain of baseDomains) {
        try {
            const response = await fetch(`https://${domain}/newIndex/index.xhtml`, {
                method: 'HEAD',
                mode: 'no-cors'  // 使用no-cors模式避免跨域问题
            });
            return domain;  // 返回第一个可用的域名
        } catch (error) {
            console.log(`域名 ${domain} 不可用`);
            continue;
        }
    }
    
    // 如果都不可用，返回默认域名
    return 'handorderg.kuaidizs.cn';
}

// 修改检查函数
async function checkNewRefundRecord() {
    if (isProcessing) {
        return;
    }
    
    const firstRowMemo = document.querySelector('.ant-table-tbody tr:first-child td:nth-child(9)');
    
    if (firstRowMemo) {
        const memoText = firstRowMemo.textContent.trim();
        const orderLink = document.querySelector('.ant-table-tbody tr:first-child a.tradeUrl___16K0h');
        
        if (orderLink) {
            const orderId = orderLink.textContent.trim();
            
            if (processedOrders.has(orderId)) {
                return;
            }
            
            try {
                isProcessing = true;
                
                // 获取产品信息
                currentProductInfo = getScanPageProductInfo();
                console.log('获取到产品信息:', currentProductInfo);
                
                // 检查应收/实收数量
                const quantityCell = document.querySelector('.ant-table-tbody tr:first-child td:nth-child(6)');
                if (quantityCell && quantityCell.textContent.trim() !== '1') {
                    const notification = showNotification('检测到多件商品，停止操作');
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    notification.remove();
                    processedOrders.add(orderId);
                    return;
                }
                
                // 检查是否是红旗无备注
                const redFlagImg = firstRowMemo.querySelector('img[src="https://assets.alicdn.com/sys/common/icon/trade/op_memo_1.png"]');
                const grayFlagImg = firstRowMemo.querySelector('img[src="https://assets.alicdn.com/sys/common/icon/trade/op_memo_0.png"]');
                
                // 检查红旗标记的情况
                if (redFlagImg) {
                    if (memoText === '') {
                        const notification = showNotification('检测到无备注，停止操作');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        notification.remove();
                        processedOrders.add(orderId);
                        return;
                    } else if (memoText !== '已标注' && memoText !== '库存') {
                        const notification = showNotification('检测到非常规备注内容，停止操作');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        notification.remove();
                        processedOrders.add(orderId);
                        return;
                    }
                }
                
                // 检查灰旗标记的情况
                if (grayFlagImg) {
                    if (memoText === '') {
                        const notification = showNotification('检测到灰旗无备注，正在入库');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        notification.remove();
                        await handleButtonClick(orderId, 'stock');
                        processedOrders.add(orderId);
                        return;
                    } else if (memoText !== '已标注' && memoText !== '库存') {
                        const notification = showNotification('检测到非常规备注内容，停止操作');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        notification.remove();
                        processedOrders.add(orderId);
                        return;
                    }
                }
                
                // 处理常规备注内容
                if (memoText.includes('已标注')) {
                    const notification = showNotification('检测到已标注，正在打单');
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    notification.remove();
                    await handleButtonClick(orderId, 'print');
                    processedOrders.add(orderId);
                } else if (memoText === '库存' || (grayFlagImg && memoText === '')) {
                    const message = grayFlagImg && memoText === '' ? '检测到灰旗无备注，正在入库' : '检测到库存备注，正在入库';
                    const notification = showNotification(message);
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    notification.remove();
                    await handleButtonClick(orderId, 'stock');
                    processedOrders.add(orderId);
                }
            } catch (error) {
                console.error('处理过程出错:', error);
            } finally {
                isProcessing = false;
            }
        }
    }
}

// 修改按钮点击处理函数
async function handleButtonClick(orderId, type) {
    if (!isExtensionEnabled) {
        return;
    }
    
    try {
        // 复制订单号到剪贴板
        await navigator.clipboard.writeText(orderId);
        
        if (type === 'stock') {
            // 发送消息给background script打开入库存相关标签页
            chrome.runtime.sendMessage({
                action: 'openStockTabs',
                orderId: orderId  // 添加订单号
            });
        } else if (type === 'print') {
            // 原有的打单功能
            chrome.runtime.sendMessage({
                action: 'openNewTab',
                url: 'https://p51.kuaidizs.cn/newIndex/index.xhtml?appkey=12158997&taobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7&taobaoId=506699383&version=premium&isNewUser=0&isFromXcx=0&subTaobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7%3A%E5%B0%8F%E4%B8%9C&subTaobaoId=2674282203&v=1737469471137#/customPrint/',
                orderId: orderId
            });
        }
    } catch (error) {
        console.error('按钮点击处理出错:', error);
    }
}

// 页面加载完成后添加按钮
document.addEventListener('DOMContentLoaded', addButtons);

// 监听页面变化，处理动态加载的内容（用于添加按钮）
const buttonObserver = new MutationObserver(addButtons);
buttonObserver.observe(document.body, { childList: true, subtree: true });

// 用于检查已标注的观察器
const refundObserver = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
        if (mutation.type === 'childList') {
            checkNewRefundRecord();
        }
    }
});

// 初始化观察器
function initObserver() {
    const tableBody = document.querySelector('.ant-table-tbody');
    if (tableBody) {
        refundObserver.observe(tableBody, {
            childList: true,
            subtree: true
        });
    } else {
        setTimeout(initObserver, 1000);
    }
}

// 添加页面焦点监听
window.addEventListener('focus', function() {
    // 重新初始化观察器
    initObserver();
});

// 开始监听
initObserver();

// 监听回车键
document.addEventListener('keydown', (event) => {
    if (event.key === 'Enter') {
        setTimeout(checkNewRefundRecord, 500);
    }
});

// 创建并显示提示元素
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(255, 77, 79, 0.9);
        color: white;
        padding: 20px 40px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 24px;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;
    notification.textContent = message;
    document.body.appendChild(notification);
    return notification;
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -60%);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%);
        }
    }
    
    @keyframes fadeOut {
        from {
            opacity: 1;
            transform: translate(-50%, -50%);
        }
        to {
            opacity: 0;
            transform: translate(-50%, -40%);
        }
    }
`;
document.head.appendChild(style);

// 添加移除按钮的函数
function removeAllButtons() {
    const buttonContainers = document.querySelectorAll('.custom-buttons-container');
    buttonContainers.forEach(container => container.remove());
}

// 修改其他函数，添加状态检查
async function copyAndSwitchToApp() {
    if (!isExtensionEnabled) return;
    // ... 原有代码 ...
}

async function handleShippingInfo() {
    if (!isExtensionEnabled) return;
    // ... 原有代码 ...
}

// 修改音频监听器，使其更可靠
let lastPlayedAudio = null;
document.addEventListener('play', function(e) {
    if (e.target.tagName === 'AUDIO') {
        lastPlayedAudio = e.target;
        const audioSrc = e.target.src;
        console.log('正在播放新音频:', audioSrc);
        
        // 记录音频信息
        if (audioSrc.includes('1')) {
            console.log('检测到1件订单');
        } else if (audioSrc.includes('2')) {
            console.log('检测到2件订单');
        } else if (audioSrc.includes('3')) {
            console.log('检测到3件订单');
        } else if (audioSrc.includes('4')) {
            console.log('检测到4件订单');
        } else if (audioSrc.includes('5')) {
            console.log('检测到5件订单');
        } else if (audioSrc.includes('6')) {
            console.log('检测到6件订单');
        } else if (audioSrc.includes('7')) {
            console.log('检测到7件订单');
        }
    }
}, true);

// 修改 getScanPageProductInfo 函数
function getScanPageProductInfo() {
    const productInfos = document.querySelectorAll('.ant-space.ant-space-vertical');
    console.log('找到的容器数量:', productInfos.length);
    
    for (const container of productInfos) {
        const text = container.textContent;
        if (text.includes('颜色分类') || text.includes('主要颜色')) {
            const titleSpan = container.querySelector('.ant-space-item:first-child span');
            const skuSpan = container.querySelector('.ant-space-item:last-child .ant-typography-secondary');
            
            if (titleSpan && skuSpan) {
                // 从标题中提取商品简称（50326格式的5位数字）
                const shortCodeMatch = titleSpan.textContent.match(/\b(\d{5})\b/);
                const shortCode = shortCodeMatch ? shortCodeMatch[1] : null;
                
                const info = {
                    title: titleSpan.textContent.trim(),
                    skuInfo: skuSpan.textContent.trim(),
                    shortCode: shortCode
                };
                
                console.log('找到产品信息:', info);
                
                // 如果找到商品简称，立即复制到剪贴板
                if (shortCode) {
                    // 使用 background 脚本来处理复制操作
                    chrome.runtime.sendMessage({
                        action: 'copyToClipboard',
                        text: shortCode
                    }, response => {
                        if (response && response.success) {
                            console.log('商品简称已复制到剪贴板:', shortCode);
                        } else {
                            console.error('复制商品简称失败:', response?.error);
                        }
                    });
                }
                
                // 存储产品信息
                chrome.storage.local.set({ 
                    currentProductInfo: info,
                    lastProductInfoTime: Date.now()
                }, function() {
                    console.log('产品信息已存储到 chrome.storage.local');
                });
                
                return info;
            }
        }
    }
    return null;
}

// 修改消息监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getScanPageProduct') {
        // 从 storage 获取产品信息
        chrome.storage.local.get(['currentProductInfo'], function(result) {
            console.log('从storage获取产品信息:', result.currentProductInfo);
            sendResponse({ productInfo: result.currentProductInfo });
        });
        return true;
    }
});

// 添加消息监听器来处理获取商品简称的请求
window.addEventListener('message', function(event) {
    if (event.data.type === 'GET_SHORT_CODE') {
        chrome.storage.local.get(['currentShortCode'], function(result) {
            console.log('content.js: storage.local.get 结果:', result);
            window.postMessage({
                type: 'SHORT_CODE_RESPONSE',
                shortCode: result.currentShortCode
            }, '*');
        });
    }
}); 