// 全局变量
let isProcessing = false;
let retryCount = 0;
const MAX_RETRIES = 10;
let lastProcessedCode = null;
let waitForInputTimer = null;
let isQueryInProgress = false;

// 添加一个变量跟踪当前显示的toast
let currentToast = null;
let currentToastTimer = null;

// 高亮样式
const highlightStyle = `
    .highlight-matched-sku {
        background-color: #fff3cd !important;
        border: 2px solid #ffc107 !important;
        box-shadow: 0 0 8px rgba(255, 193, 7, 0.5) !important;
        transition: background-color 0.3s ease;
        position: relative;
        z-index: 1;
    }
    
    .stock-added-highlight {
        background-color: #f8d7da !important;
        border: 2px solid #dc3545 !important;
        box-shadow: 0 0 8px rgba(220, 53, 69, 0.5) !important;
        transition: background-color 0.3s ease;
        position: relative;
        z-index: 1;
    }
    
    @keyframes highlight-pulse {
        0%, 100% { border-color: #ffc107; }
        50% { border-color: #ff9800; }
    }
    
    @keyframes red-highlight-pulse {
        0%, 100% { border-color: #dc3545; }
        50% { border-color: #c82333; }
    }
`;

// 主函数：初始化处理器
async function initClipboardHandler() {
    console.log('剪贴板处理器初始化...');
    
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
        await new Promise(resolve => window.addEventListener('load', resolve));
    }
    
    // 检查当前页面是否是店铺助手页面
    if (isStockAssistantPage()) {
        console.log('检测到店铺助手页面，等待页面准备完成...');
        // 等待页面准备完成（约2秒）
        setTimeout(async () => {
            // 等待输入框出现
            await waitForSearchInput();
        }, 2000);
    }
}

// 检查是否是店铺助手页面
function isStockAssistantPage() {
    const url = window.location.href;
    return url.includes('menu=stock') || url.includes('/stock/');
}

// 等待搜索输入框出现
async function waitForSearchInput() {
    console.log('等待搜索输入框出现...');
    clearTimeout(waitForInputTimer);
    
    const searchInput = document.querySelector('input[data-ref="queryStockKcgl"]');
    if (searchInput) {
        console.log('找到搜索输入框，开始处理剪贴板');
        await processClipboardInput();
    } else {
        // 如果还没找到输入框，继续等待
        if (retryCount < MAX_RETRIES) {
            retryCount++;
            console.log(`未找到搜索输入框，${retryCount}秒后重试...`);
            waitForInputTimer = setTimeout(waitForSearchInput, 1000);
        } else {
            console.log('达到最大重试次数，停止查找输入框');
            retryCount = 0; // 重置重试计数
        }
    }
}

// 主要处理逻辑
async function processClipboardInput() {
    if (isProcessing || isQueryInProgress) {
        console.log('正在处理中，跳过...');
        return;
    }
    
    try {
        isProcessing = true;
        
        // 先尝试从 storage 获取商品简称
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['currentShortCode'], resolve);
        });

        let shortCode = '';
        if (result.currentShortCode) {
            console.log('从 storage 获取到商品简称:', result.currentShortCode);
            shortCode = result.currentShortCode;
        } else {
            // 如果 storage 中没有，则尝试从剪贴板获取
            const tempTextArea = document.createElement('textarea');
            document.body.appendChild(tempTextArea);
            tempTextArea.focus();
            
            const success = document.execCommand('paste');
            if (success) {
                shortCode = tempTextArea.value;
                console.log('从剪贴板读取到内容:', shortCode);
            }
            
            document.body.removeChild(tempTextArea);
        }
        
        // 如果是相同的商品简称，跳过处理
        if (shortCode === lastProcessedCode) {
            console.log('跳过重复的商品简称:', shortCode);
            return;
        }
        
        // 验证是否有效的商品简称
        if (shortCode && shortCode.length > 0) {
            console.log('获取到商品简称:', shortCode);
            lastProcessedCode = shortCode;
            isQueryInProgress = true;
            
            // 等待输入框出现
            const searchInput = await waitForElement('input[data-ref="queryStockKcgl"]');
            if (!searchInput) {
                throw new Error('未找到搜索输入框');
            }
            
            // 设置输入框的值
            searchInput.value = shortCode;
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
            searchInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            console.log('已设置输入框的值:', shortCode);
            
            // 点击查询按钮
            const queryButton = document.querySelector('#queryStockKcgl');
            if (queryButton) {
                queryButton.click();
                console.log('已点击查询按钮');
                
                // 等待查询结果并展开详情
                await new Promise(resolve => setTimeout(resolve, 1000));
                const arrowButton = document.querySelector('a.arrowTag.big[data-act-name="arrowTag"]');
                if (arrowButton) {
                    console.log('找到展开详情按钮，准备点击');
                    try {
                        arrowButton.dispatchEvent(new Event('click', { bubbles: true }));
                        console.log('已点击展开详情按钮');
                    } catch (error) {
                        console.error('点击展开详情按钮失败:', error);
                    }
                }
            }
        } else {
            console.log('未获取到有效的商品简称，当前值:', shortCode);
        }
    } catch (error) {
        console.error('处理剪贴板时出错:', error);
    } finally {
        isProcessing = false;
        isQueryInProgress = false;
    }
}

// 添加等待元素出现的辅助函数
function waitForElement(selector, timeout = 10000, options = {}) {
    const { checkInterval = 200, logWaiting = false } = options;
    return new Promise((resolve) => {
        const startTime = Date.now();
        let checkCount = 0;

        function check() {
            checkCount++;
            const element = document.querySelector(selector);
            
            if (logWaiting && checkCount % 5 === 0) {
                console.log(`等待元素 ${selector} 中... 已等待 ${(Date.now() - startTime) / 1000}秒`);
            }
            
            if (element) {
                console.log(`元素 ${selector} 已找到，用时 ${(Date.now() - startTime) / 1000}秒`);
                resolve(element);
                return;
            }
            
            if (Date.now() - startTime > timeout) {
                console.log(`等待元素 ${selector} 超时`);
                resolve(null);
                return;
            }
            
            setTimeout(check, checkInterval);
        }
        
        check();
    });
}

// 优化SKU信息的函数
function cleanSkuInfo(skuInfo) {
    if (!skuInfo) return '';
    
    console.log('开始优化SKU信息:', skuInfo);
    let cleanedSkuInfo = skuInfo;
    
    // 处理颜色分类中的【】内容
    cleanedSkuInfo = cleanedSkuInfo.split(';').map(part => {
        // 只处理颜色分类或主要颜色部分
        if (part.includes('颜色分类') || part.includes('主要颜色')) {
            return part
                .replace(/【(.*?)】/g, (match, content) => {
                    return content.length > 10 ? '' : `【${content}】`;
                })
                .replace(/\s+/g, ' ')    // 合并多个空格
                .trim();
        }
        return part; // 其他部分保持不变
    }).join('; '); // 用分号+空格重新连接
    
    // 检查是否存在重复的SKU信息
    const commonSeparators = [',', '，', ';', '；', '|', '/'];
    
    for (const separator of commonSeparators) {
        if (cleanedSkuInfo.includes(separator)) {
            const parts = cleanedSkuInfo.split(separator).map(part => part.trim());
            // 使用Set移除重复项
            const uniqueParts = [...new Set(parts)];
            cleanedSkuInfo = uniqueParts.join(separator + ' ');
            break;
        }
    }
    
    console.log('优化后的SKU信息:', cleanedSkuInfo);
    return cleanedSkuInfo;
}

// 提取颜色信息
function extractColor(skuInfo) {
    const colorMatch = skuInfo.match(/(?:颜色分类|主要颜色):([^;【】]+)/);
    return colorMatch ? colorMatch[1].trim() : null;
}

// 提取标准尺码
function extractSize(skuInfo) {
    // 尝试直接从尺码标识提取
    const sizeMarker = skuInfo.match(/尺码:([^;]+)/);
    if (sizeMarker) {
        const sizeInfo = sizeMarker[1].trim();
        console.log('尺码标记提取:', sizeInfo);
        
        // 尝试提取标准尺码值 (忽略括号内容)
        // 支持的尺码格式: S, M, L, XL, XXL, 2XL, XXXL, 3XL, 4XL, 数字+cm
        const standardSize = sizeInfo.match(/^([SMLX]{1,3}|[XS]{2}|[2-4]?XL|\d{2,3}cm)\b/i);
        if (standardSize) {
            const result = standardSize[1].toUpperCase();
            console.log('提取到标准尺码:', result);
            return result;
        }
        
        // 如果没有标准尺码格式，返回括号前的内容
        const bareSize = sizeInfo.split(/【|\[/)[0].trim();
        console.log('提取到非标准尺码:', bareSize);
        return bareSize;
    }
    
    // 字母尺码匹配（包含2XL和3XL的写法）
    const letterSizePattern = /\b(S|M|L|XL|XXL|2XL|XXXL|3XL|4XL)\b/i;
    const letterSizeMatch = skuInfo.match(letterSizePattern);
    if (letterSizeMatch) {
        const result = letterSizeMatch[1].toUpperCase(); // 统一转为大写以便比较
        console.log('从完整SKU信息提取到尺码:', result);
        return result;
    }
    
    // CM尺码匹配
    const cmSizePattern = /\b(80|90|100|110|120|130|140|150|160|170)(?:cm|CM)\b/i;
    const cmSizeMatch = skuInfo.match(cmSizePattern);
    if (cmSizeMatch) {
        const result = cmSizeMatch[1] + 'cm'; // 统一转为小写cm
        console.log('从完整SKU信息提取到尺码:', result);
        return result;
    }
    
    console.log('未能提取到任何尺码信息');
    return null;
}

// 匹配商品行 - 优化后的函数
function findMatchingRow(color, size) {
    console.log('开始匹配，参数:', { color, size });
    if (!color && !size) {
        console.log('颜色和尺码参数都无效:', { color, size });
        return null;
    }

    // 获取所有子规格商品行
    const childRows = document.querySelectorAll('.childTr');
    console.log(`找到 ${childRows.length} 个子规格商品行`);
    
    if (childRows.length === 0) {
        console.log('未找到子规格商品行，可能需要先点击展开详情');
        return null;
    }
    
    // 先检查是否有精确匹配的行
    const exactMatches = [];
    let bestMatch = null;
    let bestScore = 0;
    
    childRows.forEach((row, index) => {
        const description = row.querySelector('.prod_list_sml dd')?.textContent;
        if (!description) return;
        
        // 解析商品描述，根据空格分离颜色和尺码
        // 店铺助手商品描述格式: "颜色名称 尺码信息" 或 "颜色名称 尺码信息【附加说明】"
        const firstSpaceIndex = description.indexOf(' ');
        
        // 如果没有空格，无法解析
        if (firstSpaceIndex === -1) {
            console.log(`[行${index+1}] 描述"${description}"没有空格，无法解析`);
            return;
        }
        
        // 提取颜色 (空格前的内容)
        const rowColor = description.substring(0, firstSpaceIndex).trim();
        
        // 提取尺码值 (空格后的内容，去除括号内容)
        let remainingText = description.substring(firstSpaceIndex + 1).trim();
        
        // 尝试提取标准尺码值
        let rowSize = '';
        
        // 匹配标准尺码格式 (S,M,L,XL,XXL,2XL,3XL,数字+cm)
        const sizeMatch = remainingText.match(/^([SMLX]{1,3}|[XS]{2}|[2-4]?XL|\d{2,3}cm)\b/i);
        if (sizeMatch) {
            rowSize = sizeMatch[1].toUpperCase();
        } else {
            // 如果没有标准格式，使用括号前的内容
            rowSize = remainingText.split(/【|\[/)[0].trim();
        }
        
        console.log(`[行${index+1}] 解析: "${description}" => 颜色:"${rowColor}", 尺码:"${rowSize}"`);
        
        // 精确尺码匹配检查
        let isExactSizeMatch = false;
        if (size && rowSize) {
            // 完全相同的尺码 (大小写不敏感)
            const normalizedSize = size.toUpperCase();
            const normalizedRowSize = rowSize.toUpperCase();
            
            if (normalizedSize === normalizedRowSize) {
                isExactSizeMatch = true;
                console.log(`[行${index+1}] 尺码精确匹配: ${normalizedSize} = ${normalizedRowSize}`);
            } else {
                // 检查是否是部分匹配 (如XL匹配到XXL)
                if (normalizedRowSize.includes(normalizedSize)) {
                    // 确保是边界匹配而不是包含匹配
                    // 例如: XL不应该匹配XXL，但应该匹配XL
                    const boundary = normalizedRowSize.indexOf(normalizedSize) + normalizedSize.length;
                    if (boundary === normalizedRowSize.length || 
                        !/[A-Z0-9]/.test(normalizedRowSize.charAt(boundary))) {
                        console.log(`[行${index+1}] 尺码边界匹配: ${normalizedSize} 在 ${normalizedRowSize} 的边界`);
                    } else {
                        console.log(`[行${index+1}] 尺码部分匹配，但不是边界匹配: ${normalizedSize} 在 ${normalizedRowSize} 中`);
                        isExactSizeMatch = false;
                    }
                }
            }
        }
        
        // 计算匹配得分
        let score = 0;
        
        // 颜色匹配评分
        if (color && rowColor) {
            // 完全匹配颜色
            if (color.toLowerCase() === rowColor.toLowerCase()) {
                score += 40;
                console.log(`[行${index+1}] 颜色完全匹配 (+40分)`);
            } 
            // 部分匹配颜色
            else if (rowColor.toLowerCase().includes(color.toLowerCase()) || 
                    color.toLowerCase().includes(rowColor.toLowerCase())) {
                score += 30;
                console.log(`[行${index+1}] 颜色部分匹配 (+30分)`);
            }
        }
        
        // 尺码匹配评分
        if (size && rowSize) {
            // 精确匹配尺码 (区分XL和XXL等)
            if (isExactSizeMatch) {
                score += 60;
                console.log(`[行${index+1}] 尺码精确匹配 (+60分)`);
                
                // 如果颜色也匹配，记录为精确匹配
                if (score >= 70) {
                    exactMatches.push({
                        row,
                        score,
                        description,
                        index
                    });
                    console.log(`[行${index+1}] 添加到精确匹配列表 (总分:${score})`);
                }
            } 
            // 部分匹配尺码但确保边界条件
            else if (description.includes(size)) {
                const sizeIndex = description.indexOf(size);
                const nextChar = description.charAt(sizeIndex + size.length);
                const prevChar = sizeIndex > 0 ? description.charAt(sizeIndex - 1) : '';
                
                // 如果尺码是独立的边界词，而不是其他尺码的一部分
                if ((!/[A-Za-z0-9]/.test(nextChar) || /[\s\[\]【】]/.test(nextChar)) && 
                    (!/[A-Za-z]/.test(prevChar) || /[\s\[\]【】]/.test(prevChar))) {
                    score += 20;
                    console.log(`[行${index+1}] 尺码边界匹配 (+20分)`);
                }
            }
        }
        
        // 完整描述包含颜色和尺码也加少量分数
        if (description.includes(color) && description.includes(size)) {
            score += 10;
            console.log(`[行${index+1}] 描述包含颜色和尺码 (+10分)`);
        }
        
        // 更新最佳匹配
        if (score > bestScore) {
            bestScore = score;
            bestMatch = row;
            console.log(`[行${index+1}] 当前最高分: ${score}`);
        }
    });
    
    // 优先返回精确匹配中得分最高的行
    if (exactMatches.length > 0) {
        // 按得分排序
        exactMatches.sort((a, b) => b.score - a.score);
        const bestExactMatch = exactMatches[0];
        console.log(`返回精确匹配行 [行${bestExactMatch.index+1}]: "${bestExactMatch.description}", 得分: ${bestExactMatch.score}`);
        return bestExactMatch.row;
    }
    
    // 如果没有精确匹配但有达到阈值的最佳匹配，返回它
    if (bestMatch && bestScore >= 50) {
        const description = bestMatch.querySelector('.prod_list_sml dd')?.textContent;
        console.log(`返回最佳匹配行: "${description}", 得分: ${bestScore}`);
        return bestMatch;
    }
    
    console.log('未找到足够匹配的行，最高分:', bestScore);
    return null;
}

// 添加高亮样式到页面
function addHighlightStyle() {
    if (!document.getElementById('highlight-style')) {
        const style = document.createElement('style');
        style.id = 'highlight-style';
        style.textContent = highlightStyle;
        document.head.appendChild(style);
        console.log('已添加高亮样式');
    }
}

// 高亮和滚动到匹配行，并增加库存
async function highlightAndScrollToRow(row) {
    if (!row || typeof row.classList === 'undefined') {
        console.error('无效的行元素:', row);
        return;
    }
    
    try {
        // 移除之前的高亮
        document.querySelectorAll('.highlight-matched-sku').forEach(el => 
            el.classList.remove('highlight-matched-sku'));
        
        // 添加高亮样式类
        row.classList.add('highlight-matched-sku');
        
        // 使用更安全的滚动方式
        try {
            // 尝试使用平滑滚动
            row.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
        } catch (scrollError) {
            // 如果平滑滚动失败，使用简单滚动
            console.log('平滑滚动失败，使用简单滚动', scrollError);
            const rect = row.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const targetY = rect.top + scrollTop - (window.innerHeight / 2);
            
            window.scrollTo(0, targetY);
        }
        
        // 添加边框闪烁动画增强可见性
        const currentStyle = row.getAttribute('style') || '';
        row.setAttribute('style', `${currentStyle}; animation: highlight-pulse 2s ease-in-out;`);
        
        console.log('已高亮并滚动到匹配行');
        
        // 等待高亮样式完全应用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 新增: 找到该行的增加库存按钮并点击
        const addStockButton = row.querySelector('a[data-act-name="addStock"].icon_add');
        if (addStockButton) {
            console.log('找到增加库存按钮，准备点击');
            addStockButton.click();
            
            // 等待增加库存弹窗出现
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // 找到库存数量输入框
            const stockInput = document.querySelector('input.mt_10.onlyNum[maxlength="7"][placeholder*="库存数量"]');
            if (stockInput) {
                console.log('找到库存数量输入框，输入数字1');
                // 设置输入值为1
                stockInput.value = '1';
                stockInput.dispatchEvent(new Event('input', { bubbles: true }));
                stockInput.dispatchEvent(new Event('change', { bubbles: true }));
                
                // 等待输入值生效
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 点击确认增加按钮
                const confirmButton = document.querySelector('button.confirm[data-act-name="confirm"]');
                if (confirmButton) {
                    console.log('找到确认增加按钮，准备点击');
                    confirmButton.click();
                    
                    // 等待弹窗关闭和页面刷新
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // 页面刷新后，重新查找并高亮行
                    console.log('库存增加完成，重新查找商品行');
                    await rehighlightRowAfterStockChange(row);
                } else {
                    console.log('未找到确认增加按钮');
                }
            } else {
                console.log('未找到库存数量输入框');
            }
        } else {
            console.log('未在匹配行找到增加库存按钮');
        }
    } catch (error) {
        console.error('高亮行或增加库存时出错:', error);
    }
}

// 在库存变更后重新高亮商品行
async function rehighlightRowAfterStockChange(originalRow) {
    try {
        // 保存原始行的信息以便重新查找
        const rowInfo = extractRowInfo(originalRow);
        console.log('提取的原始行信息:', rowInfo);
        
        if (!rowInfo.description) {
            console.log('无法提取原始行信息，无法重新高亮');
            return;
        }
        
        // 尝试从页面重新查找该行
        let newRow = null;
        let retryCount = 0;
        const maxRetries = 5;
        
        while (!newRow && retryCount < maxRetries) {
            console.log(`尝试重新查找行，第${retryCount + 1}次...`);
            
            // 查找所有子规格行
            const allRows = document.querySelectorAll('.childTr');
            console.log(`共找到${allRows.length}行子规格行`);
            
            // 遍历查找匹配的行
            for (const row of allRows) {
                const desc = row.querySelector('.prod_list_sml dd')?.textContent;
                if (desc && desc.includes(rowInfo.description)) {
                    console.log('找到匹配的行:', desc);
                    newRow = row;
                    break;
                }
            }
            
            if (!newRow) {
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        
        if (newRow) {
            console.log('找到刷新后的行，应用红色高亮');
            
            // 移除之前的所有高亮
            document.querySelectorAll('.highlight-matched-sku, .stock-added-highlight').forEach(el => {
                el.classList.remove('highlight-matched-sku');
                el.classList.remove('stock-added-highlight');
            });
            
            // 添加库存增加后的高亮样式类
            newRow.classList.add('stock-added-highlight');
            
            // 滚动到可见区域
            try {
                newRow.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            } catch (scrollError) {
                console.log('平滑滚动失败，使用简单滚动', scrollError);
                const rect = newRow.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const targetY = rect.top + scrollTop - (window.innerHeight / 2);
                
                window.scrollTo(0, targetY);
            }
            
            // 添加红色边框闪烁动画
            const currentStyle = newRow.getAttribute('style') || '';
            newRow.setAttribute('style', `${currentStyle}; animation: red-highlight-pulse 2s ease-in-out;`);
            
            return true;
        } else {
            console.log('在多次尝试后未能找到刷新的行');
            return false;
        }
    } catch (error) {
        console.error('重新高亮行时出错:', error);
        return false;
    }
}

// 从行中提取信息用于再次查找
function extractRowInfo(row) {
    try {
        const description = row.querySelector('.prod_list_sml dd')?.textContent?.trim();
        const sku = row.querySelector('.prod_list_sml dd:nth-child(2)')?.textContent?.trim();
        
        return {
            description,
            sku
        };
    } catch (error) {
        console.error('提取行信息时出错:', error);
        return {};
    }
}

// 主匹配函数 - 整合增加库存功能的版本
async function matchAndHighlightProduct(productInfo) {
    console.log('开始执行商品匹配和高亮...');
    
    // 检查产品信息
    if (!productInfo || !productInfo.skuInfo) {
        console.log('无效的产品信息');
        return false;
    }
    
    // 优化SKU信息
    const cleanedSkuInfo = cleanSkuInfo(productInfo.skuInfo);
    
    // 确保高亮样式已添加
    addHighlightStyle();
    
    // 提取颜色和尺码 (使用优化后的SKU信息)
    const color = extractColor(cleanedSkuInfo);
    const size = extractSize(cleanedSkuInfo);
    
    console.log('提取的匹配信息:', { color, size, originalSku: productInfo.skuInfo, cleanedSku: cleanedSkuInfo });
    
    if (!color && !size) {
        console.log('无法提取颜色和尺码信息');
        return false;
    }
    
    // 尝试查找展开详情按钮并点击
    const arrowButton = document.querySelector('a.arrowTag.big[data-act-name="arrowTag"]');
    if (arrowButton) {
        console.log('找到展开详情按钮，点击以展开子规格');
        try {
            arrowButton.click();
            // 等待子规格加载
            await new Promise(resolve => setTimeout(resolve, 800));
        } catch (error) {
            console.error('点击展开详情按钮失败:', error);
        }
    } else {
        console.log('未找到展开详情按钮，尝试直接查找子规格');
    }
    
    // 等待一小段时间确保展开动画完成
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 查找匹配行
    let matchingRow = null;
    let retryCount = 0;
    const maxRetries = 3;
    
    while (!matchingRow && retryCount < maxRetries) {
        matchingRow = findMatchingRow(color, size);
        
        if (!matchingRow) {
            console.log(`未找到匹配行，第 ${retryCount + 1} 次重试...`);
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }
    
    if (matchingRow) {
        console.log('找到匹配行，开始高亮并增加库存');
        await highlightAndScrollToRow(matchingRow);
        return true;
    }
    
    console.log('多次尝试后仍未找到匹配行');
    return false;
}

// 修改现有的消息监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'searchShortCode' && request.shortCode) {
        console.log('收到搜索商品简称请求:', request.shortCode);
        
        // 重置状态
        isProcessing = false;
        retryCount = 0;
        lastProcessedCode = null;
        isQueryInProgress = false;
        
        // 如果提供了SKU信息，显示临时提示
        if (request.skuInfo) {
            showSkuInfoToast(request.skuInfo);
        }
        
        // 处理搜索请求
        const searchInput = document.querySelector('input[data-ref="queryStockKcgl"]');
        if (searchInput) {
            isQueryInProgress = true;
            searchInput.value = request.shortCode;
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
            searchInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            const queryButton = document.querySelector('#queryStockKcgl');
            if (queryButton) {
                console.log('点击查询按钮搜索商品简称:', request.shortCode);
                queryButton.click();
                
                // 等待查询结果和详情展开
                setTimeout(async () => {
                    // 更新：不自动点击展开详情，而是在匹配函数中处理
                    // 这样可以避免在没有找到匹配行的情况下错误地点击
                    
                    // 等待一段时间让查询结果加载
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    console.log('查询完成，开始执行匹配');
                    if (request.skuInfo) {
                        // 构建产品信息对象
                        const productInfo = {
                            shortCode: request.shortCode,
                            skuInfo: request.skuInfo,
                            title: document.querySelector('.prod_list_sml dd')?.textContent || ''
                        };
                        
                        // 保存并执行匹配
                        chrome.storage.local.set({ currentProductInfo: productInfo }, async () => {
                            console.log('产品信息已保存，开始执行匹配');
                            await matchAndHighlightProduct(productInfo);
                        });
                    } else {
                        chrome.storage.local.get(['currentProductInfo'], async result => {
                            if (result.currentProductInfo) {
                                console.log('从storage获取产品信息，开始执行匹配');
                                await matchAndHighlightProduct(result.currentProductInfo);
                            }
                        });
                    }
                }, 1000);
            }
        }
    }
});

// 修改显示SKU信息的临时提示函数
function showSkuInfoToast(skuInfo) {
    // 如果已经有一个提示正在显示，先清除它
    if (currentToast) {
        document.body.removeChild(currentToast);
        if (currentToastTimer) {
            clearTimeout(currentToastTimer);
        }
    }

    let cleanedSkuInfo = skuInfo;
    
    // 新增：处理颜色分类中的【】内容
    cleanedSkuInfo = cleanedSkuInfo.split(';').map(part => {
        // 只处理颜色分类部分
        if (part.includes('颜色分类')) {
            return part
                .replace(/【(.*?)】/g, (match, content) => {
                    return content.length > 10 ? '' : `【${content}】`;
                })
                .replace(/\s+/g, ' ')    // 合并多个空格
                .trim();
        }
        return part; // 尺码部分保持不变
    }).join('; '); // 用分号+空格重新连接

    // 检查是否存在重复的SKU信息
    const commonSeparators = [',', '，', ';', '；', '|', '/'];
    
    for (const separator of commonSeparators) {
        if (cleanedSkuInfo.includes(separator)) {
            const parts = cleanedSkuInfo.split(separator).map(part => part.trim());
            // 使用Set移除重复项
            const uniqueParts = [...new Set(parts)];
            cleanedSkuInfo = uniqueParts.join(separator + ' ');
            break;
        }
    }
    
    // 如果没有找到分隔符但内容看起来是重复的（例如完全相同的两段文本）
    if (cleanedSkuInfo === skuInfo && skuInfo.length > 10) {
        const halfLength = Math.floor(skuInfo.length / 2);
        const firstHalf = skuInfo.substring(0, halfLength).trim();
        const secondHalf = skuInfo.substring(halfLength).trim();
        
        if (firstHalf === secondHalf || skuInfo.indexOf(firstHalf) > halfLength) {
            cleanedSkuInfo = firstHalf;
        }
    }
    
    // 创建一个容器，用于固定位置
    const toastContainer = document.createElement('div');
    toastContainer.className = 'sku-info-toast-container';
    toastContainer.style.cssText = `
        position: sticky;
        top: 10px;
        left: 0;
        width: 100%;
        z-index: 10000;
        display: flex;
        justify-content: center;
        pointer-events: none;
    `;
    
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'sku-info-toast';
    toast.style.cssText = `
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px 25px;
        border-radius: 6px;
        font-size: 20px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: fadeInOutSku 15s ease-in-out forwards;
        max-width: 80%;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.5;
        min-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: auto;
        margin-top: 10px;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInOutSku {
            0% { opacity: 0; transform: translateY(-20px); }
            5% { opacity: 1; transform: translateY(0); }
            90% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }
    `;
    document.head.appendChild(style);
    
    // 设置提示内容
    toast.textContent = `SKU信息: ${cleanedSkuInfo}`;
    
    // 将toast添加到容器中，再将容器添加到页面
    toastContainer.appendChild(toast);
    document.body.insertBefore(toastContainer, document.body.firstChild);
    
    // 保存当前显示的toast容器引用
    currentToast = toastContainer;
    
    // 20秒后移除
    currentToastTimer = setTimeout(() => {
        if (document.body.contains(toastContainer)) {
            document.body.removeChild(toastContainer);
        }
        style.remove();
        currentToast = null;
        currentToastTimer = null;
    }, 15000);
}

// 修改后的代码
async function handlePrintProcess(shippingInfo) {
    try {
        console.log('开始处理打印流程...');
        
        // 等待打印对话框完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 使用轮询方式等待按钮出现
        let oldPrintButton = null;
        let retryCount = 0;
        const maxRetries = 10;
        
        while (!oldPrintButton && retryCount < maxRetries) {
            oldPrintButton = document.querySelector('a.sure[data-act-name="oldElecPrint"]');
            if (!oldPrintButton) {
                console.log(`第 ${retryCount + 1} 次尝试查找原单号打印按钮...`);
                // 检查是否在正确的打印步骤
                const stepIndicator = document.querySelector('.reprint_alert_box') || 
                                    document.querySelector('[data-act-name="repeatPrint"]');
                
                if (stepIndicator) {
                    console.log('检测到打印对话框，继续等待按钮出现...');
                } else {
                    console.log('未检测到打印对话框，可能还在加载中...');
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                retryCount++;
            }
        }

        if (!oldPrintButton) {
            console.error('多次尝试后仍未找到原单号打印按钮');
            return;
        }

        console.log('找到原单号打印按钮，准备点击...');
        
        // 确保按钮在可见区域内
        oldPrintButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await new Promise(resolve => setTimeout(resolve, 500));

        // 尝试多种点击方式
        try {
            // 1. 使用原生点击
            oldPrintButton.click();
            console.log('方式1: 原生点击已执行');
            
            // 2. 使用鼠标事件
            oldPrintButton.dispatchEvent(new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            }));
            console.log('方式2: 鼠标事件已触发');
            
            // 3. 使用 jQuery 如果可用
            if (window.jQuery) {
                window.jQuery(oldPrintButton).trigger('click');
                console.log('方式3: jQuery点击已触发');
            }
            
            // 4. 模拟用户点击位置
            const rect = oldPrintButton.getBoundingClientRect();
            const clickEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
            });
            oldPrintButton.dispatchEvent(clickEvent);
            console.log('方式4: 精确位置点击已执行');
            
        } catch (error) {
            console.error('点击按钮时出错:', error);
        }

        // 验证点击是否成功
        console.log('等待验证点击结果...');
        const isSuccess = await validatePrintButtonClick();
        
        if (!isSuccess) {
            console.log('首次点击可能未成功，尝试备用方案...');
            
            // 备用方案：通过父元素触发
            const parentElement = oldPrintButton.parentElement;
            if (parentElement) {
                parentElement.click();
                console.log('已通过父元素触发点击');
            }
            
            // 再次验证
            await validatePrintButtonClick();
        }

    } catch (error) {
        console.error('处理打印过程出错:', error);
    }
}

// 修改验证函数，增加更多检查点
async function validatePrintButtonClick() {
    return new Promise((resolve) => {
        let checkCount = 0;
        const maxChecks = 10;
        
        function check() {
            checkCount++;
            console.log(`第 ${checkCount} 次检查点击结果...`);
            
            // 检查多个可能的后续元素
            const printerSelect = document.querySelector('select.select_system_printer');
            const printDialog = document.querySelector('.print-dialog');
            const printConfirm = document.querySelector('[data-act-name="print_confirm_submit"]');
            
            if (printerSelect || printDialog || printConfirm) {
                console.log('检测到打印对话框元素，点击成功');
                resolve(true);
                return;
            }
            
            if (checkCount >= maxChecks) {
                console.log('验证超时，未检测到预期元素');
                resolve(false);
                return;
            }
            
            setTimeout(check, 500);
        }
        
        check();
    });
}

// 添加一个函数来检查页面状态
function checkPrintDialogState() {
    const elements = {
        reprintBox: document.querySelector('.reprint_alert_box'),
        oldPrintButton: document.querySelector('a.sure[data-act-name="oldElecPrint"]'),
        printerSelect: document.querySelector('select.select_system_printer'),
        printConfirm: document.querySelector('[data-act-name="print_confirm_submit"]')
    };
    
    console.log('当前页面状态检查:', {
        hasReprintBox: !!elements.reprintBox,
        hasOldPrintButton: !!elements.oldPrintButton,
        hasPrinterSelect: !!elements.printerSelect,
        hasPrintConfirm: !!elements.printConfirm
    });
    
    return elements;
}

// 启动处理器
initClipboardHandler(); 
initClipboardHandler(); 