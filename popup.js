document.addEventListener('DOMContentLoaded', function() {
    const stopButton = document.getElementById('stopButton');
    
    // 初始化按钮状态
    chrome.storage.local.get('isExtensionEnabled', function(data) {
        // 如果是首次加载（没有存储的状态），则设置为启用状态
        if (data.isExtensionEnabled === undefined) {
            chrome.storage.local.set({ isExtensionEnabled: true });
            stopButton.classList.remove('stopping');
            stopButton.textContent = '停止执行';
        } else {
            stopButton.classList.toggle('stopping', !data.isExtensionEnabled);
            stopButton.textContent = data.isExtensionEnabled ? '停止执行' : '停止中';
        }
    });
    
    stopButton.addEventListener('click', function() {
        const isCurrentlyEnabled = !stopButton.classList.contains('stopping');
        
        // 更新按钮状态
        stopButton.classList.toggle('stopping', isCurrentlyEnabled);
        stopButton.textContent = isCurrentlyEnabled ? '停止中' : '停止执行';
        
        // 保存状态到存储
        chrome.storage.local.set({
            isExtensionEnabled: !isCurrentlyEnabled
        });
        
        // 通知后台脚本状态改变
        chrome.runtime.sendMessage({
            action: 'toggleExtension',
            enabled: !isCurrentlyEnabled
        });

        // 通知所有内容脚本状态已更改
        chrome.tabs.query({}, function(tabs) {
            tabs.forEach(function(tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'extensionStateChanged',
                    enabled: !isCurrentlyEnabled
                }).catch(() => {
                    // 忽略不能接收消息的标签页
                });
            });
        });
    });
}); 