document.addEventListener('DOMContentLoaded', function() {
    const stopButton = document.getElementById('stopButton');
    
    // 初始化按钮状态
    chrome.storage.local.get('isExtensionEnabled', function(data) {
        stopButton.classList.toggle('stopping', !data.isExtensionEnabled);
        stopButton.textContent = data.isExtensionEnabled ? '停止执行' : '停止中';
    });
    
    stopButton.addEventListener('click', function() {
        const isCurrentlyEnabled = !stopButton.classList.contains('stopping');
        
        // 更新按钮状态
        stopButton.classList.toggle('stopping', isCurrentlyEnabled);
        stopButton.textContent = isCurrentlyEnabled ? '停止中' : '停止执行';
        
        // 保存状态到存储
        chrome.storage.local.set({
            isExtensionEnabled: !isCurrentlyEnabled
        });
        
        // 通知后台脚本状态改变
        chrome.runtime.sendMessage({
            action: 'toggleExtension',
            enabled: !isCurrentlyEnabled
        });
    });
}); 