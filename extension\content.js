// 初始内容 

function addButtons() {
    // 查找订单链接元素
    const orderLinks = document.querySelectorAll('a.tradeUrl___16K0h');
    
    orderLinks.forEach(link => {
        // 检查是否已经添加过按钮
        if (link.nextElementSibling?.classList.contains('custom-buttons-container')) {
            return;
        }
        
        // 获取订单号
        const orderId = link.textContent;
        
        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'custom-buttons-container';
        
        // 创建入库存按钮
        const stockButton = document.createElement('button');
        stockButton.textContent = '入库存';
        stockButton.className = 'custom-button';
        stockButton.onclick = () => {
            handleButtonClick(orderId, 'stock');
        };
        
        // 创建打单按钮
        const printButton = document.createElement('button');
        printButton.textContent = '打单';
        printButton.className = 'custom-button';
        printButton.onclick = () => {
            handleButtonClick(orderId, 'print');
        };
        
        // 添加按钮到容器
        buttonContainer.appendChild(stockButton);
        buttonContainer.appendChild(printButton);
        
        // 将按钮容器插入到链接后面
        link.parentNode.insertBefore(buttonContainer, link.nextSibling);
    });
}

// 页面加载完成后添加按钮
document.addEventListener('DOMContentLoaded', addButtons);

// 监听页面变化，处理动态加载的内容
const observer = new MutationObserver(addButtons);
observer.observe(document.body, { childList: true, subtree: true });

// 处理按钮点击
async function handleButtonClick(orderId, type) {
    // 复制订单号到剪贴板
    await navigator.clipboard.writeText(orderId);
    
    // 发送消息给background script
    chrome.runtime.sendMessage({
        action: 'openNewTab',
        url: 'https://p51.kuaidizs.cn/newIndex/index.xhtml?appkey=12158997&taobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7&taobaoId=506699383&version=premium&isNewUser=0&isFromXcx=0&subTaobaoNick=%E5%BF%98%E8%AE%B0%E7%BE%8E%E4%B8%BD%E5%90%A7%3A%E5%B0%8F%E4%B8%9C&subTaobaoId=2674282203&v=1737469471137#/customPrint/',
        orderId: orderId
    });
}

// 添加复制和切换应用的功能
async function copyAndSwitchToApp() {
    try {
        // 发送消息给本地程序
        chrome.runtime.sendMessage({
            type: 'nativeMessage',
            action: 'switch_and_paste'
        }, response => {
            if (chrome.runtime.lastError) {
                console.error('发送消息失败:', chrome.runtime.lastError);
                return;
            }
            console.log('本地程序响应:', response);
        });
    } catch (error) {
        console.error('切换应用程序失败:', error);
    }
}

// 添加复制发货信息的函数
async function copyShippingInfo() {
    try {
        // 获取发货信息单元格
        const shippingCell = document.querySelector('td[data-item="orderDetail"]');
        if (!shippingCell) {
            console.error('未找到发货信息元素');
            return;
        }

        const shippingInfo = shippingCell.textContent.trim();
        console.log('准备复制发货信息:', shippingInfo);

        // 复制到剪贴板
        await navigator.clipboard.writeText(shippingInfo);
        console.log('发货信息已复制到剪贴板');
        return true;
    } catch (error) {
        console.error('复制发货信息失败:', error);
        return false;
    }
}

// 修改 handleShippingInfo 函数
async function handleShippingInfo() {
    console.log('开始处理发货信息');
    const copySuccess = await copyShippingInfo();
    if (copySuccess) {
        console.log('发货信息复制成功，准备切换应用');
        await copyAndSwitchToApp();
    } else {
        console.error('发货信息复制失败，无法继续处理');
    }
}

// 添加等待iframe.js初始化完成的函数
async function waitForIframeInit() {
    return new Promise(resolve => {
        let checkCount = 0;
        const maxChecks = 50; // 最多等待10秒
        
        const checkInit = () => {
            const productCheckboxes = document.querySelectorAll('input.orderInput[data-act-name="toggleOrderInput"]');
            const orderCheckbox = document.querySelector('input.input_check.packageCheckAll[data-act-name="selectAllPacks"]');
            
            // 检查是否所有复选框都已经稳定（不再被自动点击）
            if (productCheckboxes.length > 0 && orderCheckbox) {
                // 记录当前状态
                const states = Array.from(productCheckboxes).map(cb => cb.checked);
                
                setTimeout(() => {
                    // 检查状态是否改变
                    const newStates = Array.from(productCheckboxes).map(cb => cb.checked);
                    const statesMatch = states.every((state, index) => state === newStates[index]);
                    
                    if (statesMatch) {
                        console.log('复选框状态已稳定');
                        resolve();
                    } else if (checkCount < maxChecks) {
                        checkCount++;
                        checkInit();
                    } else {
                        console.log('等待超时，继续执行');
                        resolve();
                    }
                }, 200);
            } else if (checkCount < maxChecks) {
                checkCount++;
                setTimeout(checkInit, 200);
            } else {
                console.log('等待超时，继续执行');
                resolve();
            }
        };
        
        checkInit();
    });
}

async function executeSearch(orderId, retryCount = 0) {
    const maxRetries = 3;

    try {
        const searchButton = await waitForElement('#printBatchSearchBtn');
        if (!searchButton) {
            console.error('未找到查询按钮');
            return;
        }

        console.log('找到查询按钮，点击中...');
        searchButton.click();

        // 等待查询完成和iframe.js的自动操作完成
        console.log('等待查询和自动操作完成...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // 获取所有复选框的初始状态
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        console.log('当前所有复选框状态：');
        allCheckboxes.forEach((cb, index) => {
            console.log(`复选框 ${index + 1}:`, {
                className: cb.className,
                dataActName: cb.getAttribute('data-act-name'),
                checked: cb.checked
            });
        });

        // 只获取产品区域的复选框
        const productCheckboxes = Array.from(document.querySelectorAll('dl.prod_list_sml input.orderInput[data-act-name="toggleOrderInput"]'));
        console.log(`找到 ${productCheckboxes.length} 个产品复选框`);
        
        // 先记录所有产品复选框的状态
        const checkboxStates = productCheckboxes.map((checkbox, index) => {
            const dlElement = checkbox.closest('dl.prod_list_sml');
            const titleElement = dlElement?.querySelector('.packageOrder_titleShort');
            const isRateGift = titleElement && titleElement.textContent.includes('评价有礼');
            
            return {
                index,
                checkbox,
                checked: checkbox.checked,
                isRateGift,
                title: titleElement?.textContent || '无标题'
            };
        });

        console.log('产品复选框初始状态:', checkboxStates);

        // 等待1秒确保状态稳定
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 处理每个需要取消选中的复选框
        for (const state of checkboxStates) {
            if (state.checked && !state.isRateGift) {
                console.log(`准备取消勾选第 ${state.index + 1} 个产品复选框:`, state.title);
                state.checkbox.click();
                await new Promise(resolve => setTimeout(resolve, 500));
            } else {
                console.log(`跳过第 ${state.index + 1} 个复选框:`, 
                    state.isRateGift ? '评价有礼' : '未选中', 
                    state.title);
            }
        }

    } catch (error) {
        console.error('执行查询操作时出错:', error);
        
        if (retryCount < maxRetries) {
            console.log(`发生错误，第 ${retryCount + 1} 次重试查询`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            return executeSearch(orderId, retryCount + 1);
        }
    }
} 
