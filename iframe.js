console.log('iframe.js starting...');

// 检查当前环境
console.log('Current window location:', window.location.href);
console.log('Is iframe:', window.self !== window.top);
console.log('Document readyState:', document.readyState);

// 添加一个变量来跟踪当前正在处理的订单号
let currentOrderId = null;

// 定义一个函数来检查和填充表单
async function checkAndFillForm(orderId) {
    currentOrderId = orderId;
    console.log('检查表单元素，订单号:', orderId);
    
    // 等待页面加载完成
    await new Promise(resolve => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
    
    // 等待React渲染完成（等待表单出现）
    await new Promise(resolve => {
        const checkForm = () => {
            const form = document.querySelector('form.ant-form');
            if (form) {
                resolve();
            } else {
                setTimeout(checkForm, 500);
            }
        };
        checkForm();
    });
    
    await handlePopupIfExists();
    
    // 尝试多种不同的选择器组合
    const selectors = [
        // 基本选择器
        'input#sendinfo',
        '#sendinfo',
        // 带有属性的选择器
        'input[id="sendinfo"]',
        'input[placeholder="请输入"]',
        // 带有父元素的选择器
        '.ant-input-affix-wrapper input#sendinfo',
        '.ant-form-item input#sendinfo',
        // 通用类选择器
        '.ant-input.ant-input-sm[id="sendinfo"]'
    ];
    
    let input = null;
    let retryCount = 0;
    const maxRetries = 10;
    
    while (!input && retryCount < maxRetries) {
        for (const selector of selectors) {
            const found = document.querySelector(selector);
            console.log(`尝试选择器 "${selector}":`, found ? '找到' : '未找到');
            if (found) {
                input = found;
                console.log('成功找到输入框，使用选择器:', selector);
                break;
            }
        }
        
        if (!input) {
            // 如果没有找到，尝试通过遍历所有输入框来查找
            const allInputs = Array.from(document.querySelectorAll('input'));
            const targetInput = allInputs.find(inp => {
                const wrapper = inp.closest('.ant-form-item');
                const prefix = wrapper?.querySelector('.ant-input-prefix');
                return prefix?.textContent?.includes('发货信息');
            });
            
            if (targetInput) {
                input = targetInput;
                console.log('通过遍历找到输入框');
                break;
            }
        }
        
        if (!input) {
            console.log(`第 ${retryCount + 1} 次尝试未找到输入框，等待500ms后重试...`);
            await new Promise(resolve => setTimeout(resolve, 500));
            retryCount++;
        }
    }
    
    if (input) {
        console.log('找到目标输入框:', {
            id: input.id,
            className: input.className,
            dataClickPoint: input.getAttribute('data-click-point'),
            wrapper: input.closest('.ant-input-affix-wrapper')?.className
        });
        
        // 确保输入框可见和可交互
        if (input.offsetParent === null) {
            console.log('输入框不可见，等待渲染...');
            setTimeout(() => {
                if (currentOrderId === orderId) {
                    checkAndFillForm(orderId);
                }
            }, 500);
            return false;
        }
        
        // 先聚焦输入框
        input.focus();
        
        // 直接设置值并触发事件
        try {
            // 保存原始值
            const originalValue = input.value;
            
            // 设置新值
            input.value = orderId;
            
            // 创建并触发 input 事件
            const inputEvent = new Event('input', { bubbles: true });
            input.dispatchEvent(inputEvent);
            
            // 创建并触发 change 事件
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(changeEvent);
            
            // 如果值没有成功设置，尝试使用 Object.getOwnPropertyDescriptor
            if (input.value !== orderId) {
                console.log('使用 defineProperty 方式设置值');
                const descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
                descriptor.set.call(input, orderId);
            }
            
            // 再次触发所有事件
            ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                input.dispatchEvent(event);
            });
            
            console.log('输入框值已设置:', input.value);
        } catch (error) {
            console.error('设置输入框值失败:', error);
        }
        
        try {
            await handleSearchProcess(input);
        } catch (error) {
            console.error('处理搜索过程出错:', error);
        } finally {
            currentOrderId = null;
        }
        return true;
    }
    
    console.log('达到最大重试次数，仍未找到输入框');
    return false;
}

// 处理弹窗的函数
async function handlePopupIfExists() {
    const popup = document.querySelector('.ajax-pop-tip');
    if (popup && popup.style.display === 'block') {
        console.log('检测到初始弹窗，关闭中');
        const closeButton = popup.querySelector('.tip-close');
        if (closeButton) {
            closeButton.click();
            // 添加0.5秒延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            console.log('延迟0.5秒后继续');
        }
    }
}

// 修改查询处理函数
async function handleSearchProcess(input) {
    // 查找查询按钮 - 更新选择器以适应新的 Ant Design 结构
    const searchButton = input.closest('form.ant-form').querySelector('button[type="submit"].ant-btn-primary');
    if (!searchButton) {
        console.log('未找到查询按钮');
        return;
    }

    let isSearchSuccessful = false;
    let retryCount = 0;
    const maxRetries = 5;

    while (!isSearchSuccessful && retryCount < maxRetries) {
        console.log(`第 ${retryCount + 1} 次尝试查询`);
        searchButton.click();

        await new Promise(resolve => setTimeout(resolve, 1000));

        const result = await checkSearchResult();
        
        if (result.success) {
            console.log('查询成功，开始处理打印流程');
            isSearchSuccessful = true;
            await handleTemplateSelection();
        } else if (result.needWait) {
            console.log(`检测到频繁查询，等待 ${result.waitSeconds} 秒`);
            await new Promise(resolve => setTimeout(resolve, result.waitSeconds * 1000));
            
            const popup = document.querySelector('.ajax-pop-tip');
            const closeButton = popup?.querySelector('.tip-close');
            if (closeButton && popup.style.display === 'block') {
                console.log('关闭弹窗');
                closeButton.click();
                console.log('等待2秒确保弹窗完全关闭');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            retryCount++;
        } else {
            console.log('查询失败，等待2秒后重试...');
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    if (!isSearchSuccessful) {
        console.log('达到最大重试次数，查询失败');
    }
}

// 检查查询结果的函数
function checkSearchResult() {
    return new Promise((resolve) => {
        let checkCount = 0;
        const maxChecks = 15;
        const interval = setInterval(() => {
            checkCount++;
            
            // 优先检查是否出现频繁查询弹窗
            const popup = document.querySelector('.ajax-pop-tip');
            if (popup && (popup.style.display === 'block' || getComputedStyle(popup).display === 'block')) {
                console.log('检测到频繁查询弹窗');
                const modalBody = popup.querySelector('.modal-body');
                if (modalBody && modalBody.textContent.includes('订单查询过于频繁')) {
                    clearInterval(interval);
                    const waitText = modalBody.textContent;
                    const waitSeconds = parseInt(waitText.match(/(\d+)秒/)?.[1] || '10');
                    resolve({ 
                        success: false, 
                        needWait: true,
                        waitSeconds: waitSeconds
                    });
                    return;
                }
            }

            // 只有在没有弹窗的情况下才检查查询结果
            if (!popup || popup.style.display !== 'block') {
                const hasResults = document.querySelector('.ant-table-tbody tr');
                if (hasResults) {
                    console.log('检测到查询结果');
                    clearInterval(interval);
                    resolve({ success: true });
                    return;
                }
            }

            // 超时检查
            if (checkCount >= maxChecks) {
                console.log('查询检查超时');
                clearInterval(interval);
                resolve({ success: false, needWait: false });
            }
        }, 300); // 每300ms检查一次
    });
}

// 修改处理模板选择和后续打印流程
function handleTemplateSelection() {
    return new Promise((resolve) => {
        setTimeout(() => {
            const ytoTemplate = document.querySelector('dd[data-mode_listshowid="28271968"]');
            if (ytoTemplate) {
                console.log('找到圆通快递-新网点模板，点击选择');
                const radioInput = ytoTemplate.querySelector('input[type="radio"]');
                if (radioInput) {
                    radioInput.click();
                    ytoTemplate.classList.add('active', 'checked');
                    
                    // 先获取发货信息
                    const shippingInfo = extractShippingInfo();
                    if (shippingInfo) {
                        // 仅复制发货信息到剪贴板，但不发送本地消息
                        chrome.runtime.sendMessage({
                            action: 'copyToClipboard',
                            text: shippingInfo
                        }, (response) => {
                            if (response && response.success) {
                                console.log('发货信息已复制到剪贴板');
                                showToast('已复制');
                                
                                // 查找并点击重打按钮
                                const reprintButton = document.querySelector('a.reprint.J-clickPoint[data-act-name="handeOnePrintKdd"]');
                                if (reprintButton) {
                                    console.log('找到重打按钮，点击中');
                                    reprintButton.click();
                                    
                                    // 调用handlePrintProcess函数处理打印
                                    handlePrintProcess(shippingInfo);
                                } else {
                                    console.log('未找到重打按钮');
                                }
                                resolve();
                            } else {
                                resolve();
                            }
                        });
                    } else {
                        resolve();
                    }
                } else {
                    resolve();
                }
            } else {
                console.log('未找到圆通快递-新网点模板');
                resolve();
            }
        }, 300);
    });
}

// 修改handlePrintProcess函数
async function handlePrintProcess(shippingInfo) {
    console.log('开始处理打印流程...');
    
    // 延迟300ms
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 使用重试机制查找原单号打印按钮
    let oldPrintButton = null;
    let retryCount = 0;
    const maxRetries = 10; // 最大重试次数
    
    while (!oldPrintButton && retryCount < maxRetries) {
        oldPrintButton = document.querySelector('a.sure[data-act-name="oldElecPrint"]');
        if (!oldPrintButton) {
            console.log(`查找原单号打印按钮，第 ${retryCount + 1} 次尝试...`);
            await new Promise(resolve => setTimeout(resolve, 500)); // 每次重试间隔500ms
            retryCount++;
        }
    }
    
    if (oldPrintButton) {
        console.log('找到原单号打印按钮，等待500ms后点击...');
        
        // 等待500ms后再点击
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 点击原单号打印按钮
        oldPrintButton.click();
        console.log('已点击原单号打印按钮');

        // 等待弹窗出现
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 检查是否出现"无已生成的原单号"提示
        const noOrderMessage = document.querySelector('.seats_rer_list li');
        if (noOrderMessage && noOrderMessage.textContent.includes('无已生成的原单号')) {
            console.log('检测到无已生成的原单号提示，停止操作');
            return; // 直接返回，不执行后续操作
        }

        // 如果没有出现"无已生成的原单号"提示，继续执行原有流程
        console.log('继续执行打印流程...');
        
        // 确认找到并点击了原单号打印按钮后，立即发送本地消息执行粘贴操作
        chrome.runtime.sendMessage({
            type: 'nativeMessage',
            action: 'switch_and_paste'
        }, response => {
            if (chrome.runtime.lastError) {
                console.error('发送本地消息失败:', chrome.runtime.lastError);
            } else {
                console.log('本地程序响应:', response);
            }
        });
        
        // 延迟300ms后查找最终打印按钮
        setTimeout(() => {
            const finalPrintButton = document.querySelector('button.confirm[data-act-name="confirm"]');
            if (finalPrintButton) {
                console.log('找到最终打印按钮，点击中');
                finalPrintButton.click();
                
                // 后续打印处理流程
                setTimeout(() => {
                    const printerSelect = document.querySelector('select.select_system_printer');
                    if (printerSelect) {
                        console.log('找到打印机选择框，选择打标签');
                        printerSelect.value = '打标签';
                        printerSelect.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        setTimeout(() => {
                            const printExpressButton = document.querySelector('a.jn-htn-orange[data-act-name="print_confirm_submit"]');
                            if (printExpressButton) {
                                console.log('找到打印快递单按钮，点击中');
                                printExpressButton.click();
                                
                                setTimeout(() => {
                                    chrome.runtime.sendMessage({
                                        type: 'nativeMessage',
                                        action: 'click_top_left'
                                    });
                                }, 1000);
                            } else {
                                console.log('未找到打印快递单按钮');
                            }
                        }, 200);
                    } else {
                        console.log('未找到打印机选择框');
                    }
                }, 500);
            } else {
                console.log('未找到最终打印按钮');
            }
        }, 300);
    } else {
        console.log('达到最大重试次数，仍未找到原单号打印按钮');
    }
}

// 修改提取发货信息的函数
function extractShippingInfo() {
    try {
        // 获取发货信息单元格
        const shippingCell = document.querySelector('td[data-item="orderDetail"]');
        if (shippingCell) {
            const shippingInfo = shippingCell.textContent.trim();
            console.log('提取到发货信息:', shippingInfo);
            return shippingInfo;
        }
        console.log('未找到发货信息元素');
        return null;
    } catch (error) {
        console.error('提取发货信息失败:', error);
        return null;
    }
}

// 修改 showToast 函数样式
function showToast(message) {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: 10000;
        font-size: 14px;
        animation: fadeInOut 1s ease-in-out;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    `;
    document.head.appendChild(style);
    
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 1秒后移除
    setTimeout(() => {
        document.body.removeChild(toast);
        style.remove();
    }, 1000);
}

// 修改产品匹配函数，添加获取商品简称的功能
async function isProductMatch(printPageProduct, scanPageProduct) {
    if (!printPageProduct || !scanPageProduct) {
        console.log('产品信息不完整，无法比对');
        return false;
    }

    const normalizeText = text => {
        if (!text) return '';
        return text.replace(/[\s\[\]【】（）()\u4e00-\u9fa5]/g, '')  // 移除所有空格、括号和中文字符
                   .replace(/[，,。.]/g, '')  // 移除标点符号
                   .toLowerCase();  // 转换为小写
    };
    
    const printTitle = normalizeText(printPageProduct.title);
    const scanTitle = normalizeText(scanPageProduct.title);
    const printSku = normalizeText(printPageProduct.skuName);
    const scanSku = normalizeText(scanPageProduct.skuInfo);
    
    console.log('标准化后的文本比对:');
    console.log('扫描页面标题:', scanTitle);
    console.log('打印页面标题:', printTitle);
    console.log('扫描页面SKU:', scanSku);
    console.log('打印页面SKU:', printSku);
    
    const titleMatch = printTitle === scanTitle;
    const skuMatch = printSku === scanSku;
    
    console.log('匹配结果:', { titleMatch, skuMatch });
    
    // 如果匹配成功，获取并存储商品简称
    if (titleMatch && skuMatch) {
        const shortCode = printPageProduct.shortCode;
        if (shortCode) {
            console.log('匹配成功，获取到商品简称:', shortCode);
            // 存储商品简称，并等待存储完成
            try {
                await new Promise((resolve, reject) => {
                    chrome.storage.local.set({ 
                        currentShortCode: shortCode,
                        lastShortCodeTime: Date.now()
                    }, () => {
                        if (chrome.runtime.lastError) {
                            console.error('存储商品简称时发生错误:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('商品简称已成功存储到storage:', shortCode);
                            resolve();
                        }
                    });
                });
                
                // 额外等待一小段时间确保存储完全生效
                await new Promise(resolve => setTimeout(resolve, 500));
                
                return true;
            } catch (error) {
                console.error('存储商品简称失败:', error);
                return false;
            }
        } else {
            console.log('匹配成功但未获取到商品简称');
            return false;
        }
    }
    
    return false;
}

// 新增函数：处理打印机选择
async function handlePrinterSelection() {
    return new Promise((resolve, reject) => {
        let checkCount = 0;
        const maxChecks = 20; // 10秒超时
        
        const checkInterval = setInterval(() => {
            checkCount++;
            
            // 查找打印机选择弹窗
            const printerDialog = document.querySelector('div.choose-print-dialog');
            if (printerDialog) {
                // 查找打印机选择下拉框
                const printerSelect = printerDialog.querySelector('select.select_system_printer');
                if (printerSelect) {
                    // 选择指定打印机
                    const targetPrinter = Array.from(printerSelect.options)
                        .find(option => option.value === 'KM-202MD');
                    
                    if (targetPrinter) {
                        printerSelect.value = targetPrinter.value;
                        printerSelect.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        // 点击打印按钮
                        setTimeout(() => {
                            const printButton = document.querySelector('a.jn-htn-orange[data-act-name="print_confirm_submit"]');
                            if (printButton) {
                                printButton.click();
                                clearInterval(checkInterval);
                                resolve(true);
                                return;
                            }
                        }, 200);
                    }
                }
            }
            
            // 超时处理
            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
                resolve(false);
            }
        }, 500); // 每500ms检查一次
    });
}

// 修改打印发货单函数
async function handlePrintShippingOrder() {
    try {
        // 先获取商品简称
        const shortCodeResult = await new Promise(resolve => {
            chrome.storage.local.get(['currentShortCode', 'lastShortCodeTime'], resolve);
        });
        
        if (!shortCodeResult.currentShortCode || !shortCodeResult.lastShortCodeTime) {
            console.error('未找到有效的商品简称');
            return;
        }

        const fiveSecondsAgo = Date.now() - 5000;
        if (shortCodeResult.lastShortCodeTime <= fiveSecondsAgo) {
            console.error('商品简称已过期');
            return;
        }

        const shortCode = shortCodeResult.currentShortCode;
        console.log('已获取商品简称，等待打印完成后跳转:', shortCode);
        
        // 同时获取商品信息以提取SKU信息
        const productResult = await new Promise(resolve => {
            chrome.storage.local.get(['currentProductInfo'], resolve);
        });
        
        // 获取SKU信息（如果有）
        let skuInfo = '';
        if (productResult.currentProductInfo && productResult.currentProductInfo.skuInfo) {
            skuInfo = productResult.currentProductInfo.skuInfo;
            console.log('已获取SKU信息:', skuInfo);
        }
        
        // 处理打印流程
        const printButton = document.querySelector('input.loadingStatus.btn_bluebig[data-act-name="orderPrintFhd"]');
        if (!printButton) {
            console.log('未找到打印发货单按钮');
            return;
        }
        
        console.log('点击打印发货单按钮');
        printButton.click();
        
        // 等待打印流程完成
        await new Promise((resolve, reject) => {
            let printComplete = false;
            let checkCount = 0;
            const maxChecks = 20; // 最多等待10秒
            
            const checkInterval = setInterval(async () => {
                checkCount++;
                
                // 检查退款提示弹窗
                const firstConfirmButton = document.querySelector('div.ctrl-dialog-container button.confirm[data-act-name="confirm"]');
                if (firstConfirmButton) {
                    console.log('检测到退款提示弹窗，点击继续打印');
                    firstConfirmButton.click();
                }
                
                // 检查重新打印提示弹窗
                const secondConfirmButton = document.querySelector('div.reprint_alert_box a.sure[data-act-name="repeatPrint"]');
                if (secondConfirmButton) {
                    console.log('检测到重新打印提示弹窗，点击确认重新打印');
                    secondConfirmButton.click();
                }
                
                // 新增：检查打印机选择弹窗
                const printerDialog = document.querySelector('div.choose-print-dialog');
                if (printerDialog) {
                    console.log('检测到打印机选择弹窗，开始处理...');
                    const printerHandled = await handlePrinterSelection();
                    if (printerHandled) {
                        console.log('打印机选择处理完成');
                    }
                }
                
                // 检查打印完成的标志（所有弹窗都已关闭）
                const anyDialog = document.querySelector('div.ctrl-dialog-container, div.reprint_alert_box, div.choose-print-dialog');
                if (!anyDialog && checkCount > 3) { // 至少等待1.5秒
                    console.log('打印流程完成');
                    clearInterval(checkInterval);
                    printComplete = true;
                    resolve();
                }
                
                // 超时检查
                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    if (!printComplete) {
                        console.error('打印流程超时');
                        reject(new Error('打印流程超时'));
                    }
                }
            }, 500);
        });
        
        // 打印完成后，执行跳转
        console.log('准备跳转到店铺助手');
        try {
            // 先复制商品简称到剪贴板
            await chrome.runtime.sendMessage({
                action: 'copyToClipboard',
                text: shortCode
            });
            console.log('商品简称已复制到剪贴板:', shortCode);
            
            // 发送跳转消息，同时传递SKU信息（如果有）
            chrome.runtime.sendMessage({
                action: 'openAssistantTab',
                shortCode: shortCode,
                skuInfo: skuInfo
            }, function(response) {
                console.log('店铺助手页面已打开，准备搜索商品简称:', shortCode);
            });
        } catch (error) {
            console.error('跳转过程出错:', error);
        }
        
    } catch (error) {
        console.error('处理打印过程出错:', error);
    }
}

// 添加一个新的函数来等待和确认商品简称存储
async function waitForShortCodeStorage(expectedShortCode) {
    console.log('等待商品简称存储完成:', expectedShortCode);
    let retryCount = 0;
    const maxRetries = 10;
    
    while (retryCount < maxRetries) {
        const result = await new Promise(resolve => {
            chrome.storage.local.get(['currentShortCode', 'lastShortCodeTime'], resolve);
        });
        
        console.log('当前存储状态:', result);
        
        if (result.currentShortCode === expectedShortCode) {
            const fiveSecondsAgo = Date.now() - 5000;
            if (result.lastShortCodeTime > fiveSecondsAgo) {
                console.log('确认商品简称已正确存储');
                return true;
            }
        }
        
        console.log(`第 ${retryCount + 1} 次检查商品简称存储状态`);
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('等待商品简称存储超时');
    return false;
}

// 增强等待查询结果的函数，添加重试机制
async function waitForQueryResults() {
    console.log('等待查询结果加载...');
    
    // 最多尝试4次（初始尝试+3次重试）
    for (let attempt = 1; attempt <= 4; attempt++) {
        console.log(`尝试检测复选框 - 第${attempt}次尝试`);
        
        const result = await new Promise((resolve) => {
            let checkCount = 0;
            const maxChecks = 25; // 每次尝试最多等待5秒
            const interval = setInterval(() => {
                checkCount++;
                
                // 检查是否有商品列表
                const productList = document.querySelector('dl.prod_list_sml');
                const checkbox = document.querySelector('input.input_check.packageCheckAll[data-act-name="selectAllPacks"]');
                
                if (productList && checkbox) {
                    console.log(`第${attempt}次尝试成功：查询结果已加载完成`);
                    clearInterval(interval);
                    resolve(true);
                    return;
                }
                
                if (checkCount >= maxChecks) {
                    console.log(`第${attempt}次尝试超时`);
                    clearInterval(interval);
                    resolve(false);
                }
            }, 200); // 每200ms检查一次
        });
        
        // 如果找到了复选框，直接返回成功
        if (result) {
            return true;
        }
        
        // 如果是最后一次尝试且失败，直接返回失败
        if (attempt === 4) {
            console.log('所有尝试均失败，查询结果加载最终超时');
            return false;
        }
        
        // 否则等待2秒后重试
        console.log(`等待2秒后进行第${attempt + 1}次尝试...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    return false; // 以防万一的兜底返回
}

// 修改 handleBatchPrintForm 函数中处理查询结果的部分
async function handleBatchPrintForm(orderId) {
    console.log('开始处理批量打印表单，订单号:', orderId);
    
    try {
        // 1. 等待下拉框元素出现
        const select = await waitForElement('select.ptsearch_optionsel');
        if (!select) {
            console.error('未找到查询条件下拉框');
            return;
        }

        // 2. 设置下拉框值为 'tid'（订单编号）
        select.value = 'tid';
        select.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('已选择订单编号查询条件');

        // 3. 等待输入框出现并填入订单号
        const input = await waitForElement('input[name="tid"].editBatchInput');
        if (!input) {
            console.error('未找到订单号输入框');
            return;
        }

        // 4. 设置输入框的值
        input.value = orderId;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        
        // 移除 simulatedPlaceholder 类以隐藏占位符
        input.classList.remove('simulatedPlaceholder');
        
        // 隐藏占位符文本
        const placeholderSpan = input.parentElement.querySelector('.placeHolderCon');
        if (placeholderSpan) {
            placeholderSpan.style.display = 'none';
        }
        console.log('已填入订单号:', orderId);

        // 5. 执行查询操作
        const searchButton = document.querySelector('#printBatchSearchBtn');
        if (searchButton) {
            console.log('找到查询按钮，点击中...');
            searchButton.click();
            
            // 等待查询结果加载完成（增强版，支持多次重试）
            const querySuccess = await waitForQueryResults();
            if (!querySuccess) {
                console.error('查询结果加载最终超时，已尝试多次重试');
                return;
            }
            
            // 6. 处理复选框
            console.log('开始处理复选框...');
            await new Promise(resolve => setTimeout(resolve, 500)); // 增加等待时间，确保页面元素稳定
            
            // 先处理包裹复选框
            const packageCheckbox = document.querySelector('input.input_check.packageCheck[data-act-name="togglePackageSelectStatus"]');
            if (packageCheckbox) {
                console.log('找到包裹复选框，确保选中状态');
                if (!packageCheckbox.checked) {
                    packageCheckbox.click();
                    await new Promise(resolve => setTimeout(resolve, 500)); // 增加等待时间
                }
            } else {
                console.log('未找到包裹复选框，可能页面结构有变化');
            }

            // 获取所有商品复选框
            const productCheckboxes = document.querySelectorAll('input.orderInput[data-act-name="toggleOrderInput"]');
            console.log(`找到 ${productCheckboxes.length} 个商品复选框`);

            // 先取消选中所有复选框
            for (const checkbox of productCheckboxes) {
                if (checkbox.checked) {
                    console.log('取消选中复选框');
                    checkbox.click();
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            // 从 storage 获取产品信息
            chrome.storage.local.get(['currentProductInfo', 'lastProductInfoTime'], async function(result) {
                if (result.currentProductInfo && result.lastProductInfoTime) {
                    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
                    if (result.lastProductInfoTime > fiveMinutesAgo) {
                        const scanPageProduct = result.currentProductInfo;
                        console.log('获取到有效的产品信息:', scanPageProduct);

                        // 遍历所有商品，查找匹配的产品
                        let matchFound = false;
                        for (const checkbox of productCheckboxes) {
                            const dlElement = checkbox.closest('dl.prod_list_sml');
                            if (!dlElement) continue;

                            const img = dlElement.querySelector('img.goodsDetailTrigger');
                            if (!img) continue;

                            const printPageProduct = {
                                title: img.getAttribute('data-title') || '',
                                skuName: img.getAttribute('data-sku-name') || '',
                                shortCode: img.getAttribute('data-title-short') || ''
                            };

                            if (await isProductMatch(printPageProduct, scanPageProduct)) {
                                console.log('找到匹配的产品，勾选复选框');
                                if (!checkbox.checked) {
                                    checkbox.click();
                                    await new Promise(resolve => setTimeout(resolve, 200));
                                }
                                matchFound = true;

                                // 等待确认商品简称已正确存储
                                const storageConfirmed = await waitForShortCodeStorage(printPageProduct.shortCode);
                                if (storageConfirmed) {
                                    console.log('商品简称存储确认完成，开始打印发货单');
                                    await handlePrintShippingOrder();
                                }
                                break;
                            }
                        }

                        if (!matchFound) {
                            console.log('未找到匹配的产品');
                        }
                    } else {
                        console.log('产品信息已过期（超过5分钟）');
                    }
                } else {
                    console.log('未找到有效的产品信息');
                }
            });
        } else {
            console.error('未找到查询按钮');
        }
    } catch (error) {
        console.error('处理批量打印表单时出错:', error);
    }
}

// 添加一个函数来解析产品信息
function parseProductInfo(element) {
    const img = element.querySelector('img.goodsDetailTrigger');
    if (!img) return null;

    return {
        title: img.getAttribute('data-title') || '',
        skuName: img.getAttribute('data-sku-name') || '',
        shortTitle: img.getAttribute('data-title-short') || ''
    };
}

// 添加等待元素出现的辅助函数
function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve) => {
        if (document.querySelector(selector)) {
            return resolve(document.querySelector(selector));
        }

        const observer = new MutationObserver(() => {
            if (document.querySelector(selector)) {
                observer.disconnect();
                resolve(document.querySelector(selector));
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 设置超时
        setTimeout(() => {
            observer.disconnect();
            resolve(null);
        }, timeout);
    });
}

// 修改消息监听器，移除复选框处理逻辑
window.addEventListener('message', function(event) {
    console.log('Received message:', event.data);
    
    if (event.data.type === 'FILL_ORDER_ID') {
        const orderId = event.data.orderId;
        console.log('Got order ID:', orderId);
        
        // 检查当前页面URL是否包含 printBatch
        if (window.location.href.includes('/printBatch/')) {
            handleBatchPrintForm(orderId);
        } else if (window.location.href.includes('/customPrint/')) {
            // 原有的打单表单处理
            checkAndFillForm(orderId);
        }
    }
});

// 如果在iframe中，发送就绪消息
if (window.self !== window.top) {
    window.parent.postMessage({ type: 'IFRAME_READY' }, '*');
}