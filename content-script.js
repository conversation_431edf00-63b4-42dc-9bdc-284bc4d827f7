async function copyAndSwitchToApp() {
    try {
        // 先发送切换到第三方应用的消息
        await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'nativeMessage',
                action: 'switch_and_paste'
            }, response => {
                if (chrome.runtime.lastError) {
                    console.error('发送消息失败:', chrome.runtime.lastError);
                } else {
                    console.log('本地程序响应:', response);
                }
                resolve();
            });
        });

        // 等待一段时间后切换回扫描页面
        setTimeout(() => {
            console.log('准备切换回扫描页面');
            chrome.runtime.sendMessage({
                action: 'focusScanPage'
            }, response => {
                console.log('切换回扫描页面响应:', response);
            });
        }, 800); // 稍微增加延迟确保第三方应用有足够时间处理

    } catch (error) {
        console.error('切换应用程序失败:', error);
    }
}

// 在复制完发货信息后调用
async function handleShippingInfo() {
    // ... existing code ...
    await copyShippingInfo();
    await copyAndSwitchToApp();
}
// ... existing code ... 